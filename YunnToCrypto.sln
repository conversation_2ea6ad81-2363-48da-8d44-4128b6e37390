
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.28315.86
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Engine", "Engine", "{94A6C6F3-99B3-346E-9557-ABF9D4064DBD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Games", "Games", "{8E2F6A87-1826-34F4-940C-CC23A48F9FE4}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "UE4", "Intermediate\ProjectFiles\UE4.vcxproj", "{A4C796F7-0076-4A68-8EBC-995C7042A072}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "YunnToCrypto", "Intermediate\ProjectFiles\YunnToCrypto.vcxproj", "{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Visualizers", "Visualizers", "{1CCEC849-CC72-4C59-8C36-2F7C38706D4C}"
	ProjectSection(SolutionItems) = preProject
		C:\Program Files\Epic Games\UE_4.26\Engine\Extras\VisualStudioDebugging\UE4.natvis = C:\Program Files\Epic Games\UE_4.26\Engine\Extras\VisualStudioDebugging\UE4.natvis
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		DebugGame Editor|IOS = DebugGame Editor|IOS
		DebugGame Editor|Win32 = DebugGame Editor|Win32
		DebugGame Editor|Win64 = DebugGame Editor|Win64
		DebugGame|IOS = DebugGame|IOS
		DebugGame|Win32 = DebugGame|Win32
		DebugGame|Win64 = DebugGame|Win64
		Development Editor|IOS = Development Editor|IOS
		Development Editor|Win32 = Development Editor|Win32
		Development Editor|Win64 = Development Editor|Win64
		Development|IOS = Development|IOS
		Development|Win32 = Development|Win32
		Development|Win64 = Development|Win64
		Shipping|IOS = Shipping|IOS
		Shipping|Win32 = Shipping|Win32
		Shipping|Win64 = Shipping|Win64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.DebugGame Editor|IOS.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.DebugGame Editor|Win32.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.DebugGame Editor|Win64.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.DebugGame|IOS.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.DebugGame|Win32.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.DebugGame|Win64.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.Development Editor|IOS.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.Development Editor|Win32.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.Development Editor|Win64.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.Development|IOS.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.Development|Win32.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.Development|Win64.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.Shipping|IOS.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.Shipping|Win32.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{A4C796F7-0076-4A68-8EBC-995C7042A072}.Shipping|Win64.ActiveCfg = BuiltWithUnrealBuildTool|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.DebugGame Editor|IOS.ActiveCfg = Invalid|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.DebugGame Editor|Win32.ActiveCfg = Invalid|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.DebugGame Editor|Win64.ActiveCfg = DebugGame_Editor|x64
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.DebugGame Editor|Win64.Build.0 = DebugGame_Editor|x64
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.DebugGame|IOS.ActiveCfg = IOS_DebugGame|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.DebugGame|IOS.Build.0 = IOS_DebugGame|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.DebugGame|Win32.ActiveCfg = DebugGame|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.DebugGame|Win32.Build.0 = DebugGame|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.DebugGame|Win64.ActiveCfg = DebugGame|x64
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.DebugGame|Win64.Build.0 = DebugGame|x64
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Development Editor|IOS.ActiveCfg = Invalid|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Development Editor|Win32.ActiveCfg = Invalid|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Development Editor|Win64.ActiveCfg = Development_Editor|x64
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Development Editor|Win64.Build.0 = Development_Editor|x64
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Development|IOS.ActiveCfg = IOS_Development|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Development|IOS.Build.0 = IOS_Development|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Development|Win32.ActiveCfg = Development|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Development|Win32.Build.0 = Development|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Development|Win64.ActiveCfg = Development|x64
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Development|Win64.Build.0 = Development|x64
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Shipping|IOS.ActiveCfg = IOS_Shipping|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Shipping|IOS.Build.0 = IOS_Shipping|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Shipping|Win32.ActiveCfg = Shipping|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Shipping|Win32.Build.0 = Shipping|Win32
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Shipping|Win64.ActiveCfg = Shipping|x64
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}.Shipping|Win64.Build.0 = Shipping|x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A4C796F7-0076-4A68-8EBC-995C7042A072} = {94A6C6F3-99B3-346E-9557-ABF9D4064DBD}
		{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB} = {8E2F6A87-1826-34F4-940C-CC23A48F9FE4}
	EndGlobalSection
EndGlobal
