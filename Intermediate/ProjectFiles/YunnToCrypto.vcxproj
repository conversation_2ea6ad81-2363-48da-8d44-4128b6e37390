<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Invalid|Win32">
      <Configuration>Invalid</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Invalid|x64">
      <Configuration>Invalid</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame|Win32">
      <Configuration>DebugGame</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame|x64">
      <Configuration>DebugGame</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame_Editor|Win32">
      <Configuration>DebugGame_Editor</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame_Editor|x64">
      <Configuration>DebugGame_Editor</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="IOS_DebugGame|Win32">
      <Configuration>IOS_DebugGame</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="IOS_DebugGame|x64">
      <Configuration>IOS_DebugGame</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development|Win32">
      <Configuration>Development</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development|x64">
      <Configuration>Development</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development_Editor|Win32">
      <Configuration>Development_Editor</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development_Editor|x64">
      <Configuration>Development_Editor</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="IOS_Development|Win32">
      <Configuration>IOS_Development</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="IOS_Development|x64">
      <Configuration>IOS_Development</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|Win32">
      <Configuration>Shipping</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|x64">
      <Configuration>Shipping</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="IOS_Shipping|Win32">
      <Configuration>IOS_Shipping</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="IOS_Shipping|x64">
      <Configuration>IOS_Shipping</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2E1EA18A-42A6-4FAE-A56F-D01CCA12C5DB}</ProjectGuid>
    <Keyword>MakeFileProj</Keyword>
    <RootNamespace>YunnToCrypto</RootNamespace>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
    <MinimumVisualStudioVersion>15.0</MinimumVisualStudioVersion>
    <NMakeUseOemCodePage>true</NMakeUseOemCodePage>
    <TargetRuntime>Native</TargetRuntime>
  </PropertyGroup>
  <ItemGroup>
    <ProjectCapability Include="MLProject" />
    <PropertyPageSchema Include="$(LOCALAPPDATA)\Microsoft\VisualStudio\MagicLeap\debugger.xaml" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Invalid|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Invalid|x64'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|x64'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|x64'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IOS_DebugGame|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IOS_DebugGame|x64'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|x64'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|x64'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IOS_Development|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IOS_Development|x64'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IOS_Shipping|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IOS_Shipping|x64'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '15.0'">v141</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings" />
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Invalid|Win32'">
    <NMakeBuildCommandLine>echo The selected platform/configuration is not valid for this target.</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>echo The selected platform/configuration is not valid for this target.</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>echo The selected platform/configuration is not valid for this target.</NMakeCleanCommandLine>
    <NMakeOutput>Invalid Output</NMakeOutput>
    <OutDir>C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Unused\</OutDir>
    <IntDir>C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Unused\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Invalid|x64'">
    <NMakeBuildCommandLine>echo The selected platform/configuration is not valid for this target.</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>echo The selected platform/configuration is not valid for this target.</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>echo The selected platform/configuration is not valid for this target.</NMakeCleanCommandLine>
    <NMakeOutput>Invalid Output</NMakeOutput>
    <OutDir>C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Unused\</OutDir>
    <IntDir>C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Unused\</IntDir>
  </PropertyGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|Win32'">
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <OutDir>$(ProjectDir)..\Build\Unused\</OutDir>
    <IntDir>$(ProjectDir)..\Build\Unused\</IntDir>
    <NMakeBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Build.bat" YunnToCrypto Win32 DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Rebuild.bat" YunnToCrypto Win32 DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Clean.bat" YunnToCrypto Win32 DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win32\YunnToCrypto-Win32-DebugGame.exe</NMakeOutput>
  </PropertyGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|x64'">
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <OutDir>$(ProjectDir)..\Build\Unused\</OutDir>
    <IntDir>$(ProjectDir)..\Build\Unused\</IntDir>
    <NMakeBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Build.bat" YunnToCrypto Win64 DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Rebuild.bat" YunnToCrypto Win64 DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Clean.bat" YunnToCrypto Win64 DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\YunnToCrypto-Win64-DebugGame.exe</NMakeOutput>
  </PropertyGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|x64'">
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <OutDir>$(ProjectDir)..\Build\Unused\</OutDir>
    <IntDir>$(ProjectDir)..\Build\Unused\</IntDir>
    <NMakeBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Build.bat" YunnToCryptoEditor Win64 DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Rebuild.bat" YunnToCryptoEditor Win64 DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Clean.bat" YunnToCryptoEditor Win64 DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>C:\Program Files\Epic Games\UE_4.26\Engine\Binaries\Win64\UE4Editor-Win64-DebugGame.exe</NMakeOutput>
  </PropertyGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='IOS_DebugGame|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IOS_DebugGame|Win32'">
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <OutDir>$(ProjectDir)..\Build\Unused\</OutDir>
    <IntDir>$(ProjectDir)..\Build\Unused\</IntDir>
    <NMakeBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Build.bat" YunnToCrypto IOS DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Rebuild.bat" YunnToCrypto IOS DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Clean.bat" YunnToCrypto IOS DebugGame -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\IOS\YunnToCrypto-IOS-DebugGame</NMakeOutput>
  </PropertyGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Development|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|Win32'">
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <OutDir>$(ProjectDir)..\Build\Unused\</OutDir>
    <IntDir>$(ProjectDir)..\Build\Unused\</IntDir>
    <NMakeBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Build.bat" YunnToCrypto Win32 Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Rebuild.bat" YunnToCrypto Win32 Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Clean.bat" YunnToCrypto Win32 Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win32\YunnToCrypto.exe</NMakeOutput>
  </PropertyGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Development|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|x64'">
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <OutDir>$(ProjectDir)..\Build\Unused\</OutDir>
    <IntDir>$(ProjectDir)..\Build\Unused\</IntDir>
    <NMakeBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Build.bat" YunnToCrypto Win64 Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Rebuild.bat" YunnToCrypto Win64 Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Clean.bat" YunnToCrypto Win64 Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\YunnToCrypto.exe</NMakeOutput>
  </PropertyGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|x64'">
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <OutDir>$(ProjectDir)..\Build\Unused\</OutDir>
    <IntDir>$(ProjectDir)..\Build\Unused\</IntDir>
    <NMakeBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Build.bat" YunnToCryptoEditor Win64 Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Rebuild.bat" YunnToCryptoEditor Win64 Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Clean.bat" YunnToCryptoEditor Win64 Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>C:\Program Files\Epic Games\UE_4.26\Engine\Binaries\Win64\UE4Editor.exe</NMakeOutput>
  </PropertyGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='IOS_Development|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IOS_Development|Win32'">
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <OutDir>$(ProjectDir)..\Build\Unused\</OutDir>
    <IntDir>$(ProjectDir)..\Build\Unused\</IntDir>
    <NMakeBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Build.bat" YunnToCrypto IOS Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Rebuild.bat" YunnToCrypto IOS Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Clean.bat" YunnToCrypto IOS Development -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\IOS\YunnToCrypto</NMakeOutput>
  </PropertyGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <OutDir>$(ProjectDir)..\Build\Unused\</OutDir>
    <IntDir>$(ProjectDir)..\Build\Unused\</IntDir>
    <NMakeBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Build.bat" YunnToCrypto Win32 Shipping -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Rebuild.bat" YunnToCrypto Win32 Shipping -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Clean.bat" YunnToCrypto Win32 Shipping -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win32\YunnToCrypto-Win32-Shipping.exe</NMakeOutput>
  </PropertyGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <OutDir>$(ProjectDir)..\Build\Unused\</OutDir>
    <IntDir>$(ProjectDir)..\Build\Unused\</IntDir>
    <NMakeBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Build.bat" YunnToCrypto Win64 Shipping -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Rebuild.bat" YunnToCrypto Win64 Shipping -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Clean.bat" YunnToCrypto Win64 Shipping -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\YunnToCrypto-Win64-Shipping.exe</NMakeOutput>
  </PropertyGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='IOS_Shipping|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='IOS_Shipping|Win32'">
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <OutDir>$(ProjectDir)..\Build\Unused\</OutDir>
    <IntDir>$(ProjectDir)..\Build\Unused\</IntDir>
    <NMakeBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Build.bat" YunnToCrypto IOS Shipping -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Rebuild.bat" YunnToCrypto IOS Shipping -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>"C:\Program Files\Epic Games\UE_4.26\Engine\Build\BatchFiles\Clean.bat" YunnToCrypto IOS Shipping -Project="$(SolutionDir)YunnToCrypto.uproject" -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\IOS\YunnToCrypto-IOS-Shipping</NMakeOutput>
  </PropertyGroup>
  <PropertyGroup>
    <NMakePreprocessorDefinitions>$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <IncludePath>$(IncludePath);C:\Program Files\Epic Games\UE_4.26\Engine\Source;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\CoreUObject;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CoreUObject\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Core;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\TraceLog\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\TraceLog;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Engine;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\NetCore;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Net;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Net\Core\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Net\Core\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\ApplicationCore;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\RHI;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RHI\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Json;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Json\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\SlateCore;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\InputCore;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputCore\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputCore\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Slate;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\ImageWrapper;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ImageWrapper\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Messaging;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Messaging\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\MessagingCommon;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MessagingCommon\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\RenderCore;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RenderCore\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AnalyticsET;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Analytics;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Analytics\AnalyticsET\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Analytics;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Analytics\Analytics\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Sockets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Sockets\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\NetCommon;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Net\Common\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AssetRegistry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AssetRegistry\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\EngineMessages;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\EngineMessages\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\EngineSettings;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\EngineSettings\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\EngineSettings\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\SynthBenchmark;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SynthBenchmark\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Renderer;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Renderer\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\GameplayTags;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\GameplayTags\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\GameplayTags\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\PacketHandler;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\PacketHandler\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\PacketHandler\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\ReliabilityHandlerComponent;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\ReliabilityHandlerComponent\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AudioPlatformConfiguration;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioPlatformConfiguration\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\MeshDescription;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MeshDescription\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\StaticMeshDescription;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\StaticMeshDescription\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\PakFile;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PakFile\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\RSA;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RSA\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\NetworkReplayStreaming;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NetworkReplayStreaming;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NetworkReplayStreaming\NetworkReplayStreaming\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\PhysicsCore;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PhysicsCore\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\DeveloperSettings;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\DeveloperSettings\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Chaos;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\ChaosCore;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\ChaosCore\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\Intel;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Voronoi;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Voronoi\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\SignalProcessing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SignalProcessing\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AudioExtensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioExtensions\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AudioMixerCore;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioMixerCore\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\PropertyAccess;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PropertyAccess\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\UnrealEd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Programs\UnrealLightmass\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Android\AndroidDeviceDetection\Public\Interfaces;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\DirectoryWatcher;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DirectoryWatcher\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Documentation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Documentation\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Projects;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Projects\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\SandboxFile;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SandboxFile\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\EditorStyle;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\EditorStyle\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\SourceControl;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SourceControl\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\UnrealEdMessages;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEdMessages\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEdMessages\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\GameplayDebugger;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\GameplayDebugger\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\BlueprintGraph;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\BlueprintGraph\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\BlueprintGraph\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\EditorSubsystem;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\EditorSubsystem\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\HTTP;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\UnrealAudio;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\UnrealAudio\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\FunctionalTesting;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\FunctionalTesting\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\FunctionalTesting\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AutomationController;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AutomationController\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Localization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Localization\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AudioEditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AudioEditor\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AudioEditor\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AudioMixer;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioMixer\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioMixer\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\TargetPlatform;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TargetPlatform\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\LevelEditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\LevelEditor\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Settings;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Settings\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\IntroTutorials;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\IntroTutorials\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\HeadMountedDisplay;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\HeadMountedDisplay\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\VREditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\VREditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\VREditor\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\CommonMenuExtensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\CommonMenuExtensions\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Landscape;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Landscape\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Landscape\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\PropertyEditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PropertyEditor\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\ActorPickerMode;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ActorPickerMode\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\SceneDepthPickerMode;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SceneDepthPickerMode\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\DetailCustomizations;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\DetailCustomizations\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\ClassViewer;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ClassViewer\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\GraphEditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\GraphEditor\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\StructViewer;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\StructViewer\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\ContentBrowser;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ContentBrowser\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\ContentBrowserData;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ContentBrowserData\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\CollectionManager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\CollectionManager\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\UELibSampleRate;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\libSampleRate\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\NetworkFileSystem;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NetworkFileSystem\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\UMG;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\UMG\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\MovieScene;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\TimeManagement;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\TimeManagement\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\MovieSceneTracks;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieSceneTracks\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AnimationCore;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AnimationCore\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\PropertyPath;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PropertyPath\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\NavigationSystem;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NavigationSystem\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\MeshBuilder;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshBuilder\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\MaterialShaderQualitySettings;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MaterialShaderQualitySettings\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\InteractiveToolsFramework;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\InteractiveToolsFramework\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\ToolMenusEditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ToolMenusEditor\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\ToolMenus;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ToolMenus\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\XAudio2;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Windows\XAudio2\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AudioMixerXAudio2;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AssetTagsEditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AssetTagsEditor\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AddContentDialog;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AddContentDialog\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\MeshUtilities;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\Intel\Embree\Embree2140\Win64\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshUtilities\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\MeshMergeUtilities;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshMergeUtilities\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\HierarchicalLODUtilities;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\HierarchicalLODUtilities\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\MeshReductionInterface;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshReductionInterface\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AssetTools;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AssetTools\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\KismetCompiler;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\KismetCompiler\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\GameplayTasks;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\GameplayTasks\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\GameplayTasks\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\AIModule;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Kismet;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Kismet\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Kismet\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\ClothingSystemRuntimeInterface;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ClothingSystemRuntimeInterface\Public;..\..\Plugins\WebView\Source\WebView\Private;..\..\Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Inc\WebView;..\..\Plugins\WebView\Source;..\..\Plugins\WebView\Source\WebView\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Serialization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Serialization\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Cbor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Cbor\Public;..\..\Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Inc\JsonLibrary;..\..\Plugins\JsonLibrary\Source;..\..\Plugins\JsonLibrary\Source\JsonLibrary\Public;..\..\Plugins\JsonLibrary\Source\JsonLibrary\Private;..\..\Plugins\BPFunctionLib\Source;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\Networking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Networking\Public;..\..\Plugins\BPFunctionLib\Intermediate\Build\Win64\UE4Editor\Inc\NetConnection;..\..\Plugins\BPFunctionLib\Source\NetConnection\Public;..\..\Plugins\BlueprintHttpServer\Source\BlueprintHttpServer\Private;..\..\Plugins\BlueprintHttpServer\Source\ThirdParty\cpp-httplib\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\OpenSSL\1.1.1\include\Win64\VS2015;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\zlib\v1.2.8\include\Win64\VS2015;..\..\Plugins\BlueprintHttpServer\Intermediate\Build\Win64\UE4Editor\Inc\BlueprintHttpServer;..\..\Plugins\BlueprintHttpServer\Source;..\..\Plugins\BlueprintHttpServer\Source\BlueprintHttpServer\Public;..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\JsonUtilities;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\JsonUtilities\Public;..\..\Plugins\BPFunctionLib\Intermediate\Build\Win64\UE4Editor\Inc\BPFunctionLib;..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\ThirdParty\MyIphlpapilib\includes;..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\Public;..\..\Plugins\JsonLibrary\Source\JsonLibraryBlueprintSupport\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Intermediate\Build\Win64\UE4Editor\Inc\KismetWidgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\KismetWidgets\Public;..\..\Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Inc\JsonLibraryBlueprintSupport;..\..\Plugins\JsonLibrary\Source\JsonLibraryBlueprintSupport\Public;..\..\Plugins\BPFunctionLib\Source\NetConnection\Private;..\Build\Win64\UE4Editor\Inc\YunnToCrypto;..\..\Source;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.39.33519\INCLUDE;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um;C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt;C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared;C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um;C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt;;</IncludePath>
    <NMakeForcedIncludes>$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeAssemblySearchPath>$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <AdditionalOptions>/std:c++14</AdditionalOptions>
  </PropertyGroup>
  <ItemGroup>
    <None Include="..\..\YunnToCrypto.uproject"/>
    <None Include="..\..\Source\YunnToCrypto.Target.cs"/>
    <None Include="..\..\Source\YunnToCryptoEditor.Target.cs"/>
    <None Include="..\..\Config\DefaultEditor.ini"/>
    <None Include="..\..\Config\DefaultEngine.ini"/>
    <None Include="..\..\Config\DefaultGame.ini"/>
    <None Include="..\..\Plugins\BlueprintHttpServer\Source\BlueprintHttpServer\BlueprintHttpServer.Build.cs"/>
    <ClCompile Include="..\..\Plugins\BlueprintHttpServer\Source\BlueprintHttpServer\Private\BlueprintHttpNodes.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\BlueprintHttpServer\Intermediate\Build\Win64\UE4Editor\Development\BlueprintHttpServer\Definitions.BlueprintHttpServer.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\BlueprintHttpServer\Source\BlueprintHttpServer\Private\BlueprintHttpNodes.h"/>
    <ClCompile Include="..\..\Plugins\BlueprintHttpServer\Source\BlueprintHttpServer\Private\BlueprintHttpServer.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\BlueprintHttpServer\Intermediate\Build\Win64\UE4Editor\Development\BlueprintHttpServer\Definitions.BlueprintHttpServer.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\BlueprintHttpServer\Source\BlueprintHttpServer\Private\BlueprintHttpServerModule.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\BlueprintHttpServer\Intermediate\Build\Win64\UE4Editor\Development\BlueprintHttpServer\Definitions.BlueprintHttpServer.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\BlueprintHttpServer\Source\BlueprintHttpServer\Private\BlueprintHttpsServer.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\BlueprintHttpServer\Intermediate\Build\Win64\UE4Editor\Development\BlueprintHttpServer\Definitions.BlueprintHttpServer.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\BlueprintHttpServer\Source\BlueprintHttpServer\Public\BlueprintHttpServer.h"/>
    <ClInclude Include="..\..\Plugins\BlueprintHttpServer\Source\BlueprintHttpServer\Public\BlueprintHttpServerModule.h"/>
    <ClInclude Include="..\..\Plugins\BlueprintHttpServer\Source\BlueprintHttpServer\Public\BlueprintHttpsServer.h"/>
    <None Include="..\..\Plugins\BlueprintHttpServer\BlueprintHttpServer.uplugin"/>
    <None Include="..\..\Plugins\BlueprintHttpServer\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\BPFunctionLib\BPFunctionLib.uplugin"/>
    <None Include="..\..\Plugins\BPFunctionLib\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\JsonLibrary\JsonLibrary.uplugin"/>
    <None Include="..\..\Plugins\WebView\WebView.uplugin"/>
    <None Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\BPFunctionLib.Build.cs"/>
    <ClCompile Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\Private\BPFunctionLib.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\BPFunctionLib\Intermediate\Build\Win64\UE4Editor\Development\BPFunctionLib\Definitions.BPFunctionLib.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\Private\BPFunctionLibBPLibrary.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\BPFunctionLib\Intermediate\Build\Win64\UE4Editor\Development\BPFunctionLib\Definitions.BPFunctionLib.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\Private\MiniMapWidgetBase.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\BPFunctionLib\Intermediate\Build\Win64\UE4Editor\Development\BPFunctionLib\Definitions.BPFunctionLib.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\Private\YunntoGameInstance.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\BPFunctionLib\Intermediate\Build\Win64\UE4Editor\Development\BPFunctionLib\Definitions.BPFunctionLib.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\Public\BPFunctionLib.h"/>
    <ClInclude Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\Public\BPFunctionLibBPLibrary.h"/>
    <ClInclude Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\Public\MiniMapWidgetBase.h"/>
    <ClInclude Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\Public\YunntoGameInstance.h"/>
    <ClInclude Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\ThirdParty\MyIphlpapilib\Includes\ipexport.h"/>
    <ClInclude Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\ThirdParty\MyIphlpapilib\Includes\Iphlpapi.h"/>
    <ClInclude Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\ThirdParty\MyIphlpapilib\Includes\iptypes.h"/>
    <None Include="..\..\Plugins\BPFunctionLib\Source\BPFunctionLib\ThirdParty\MyIphlpapilib\Libs\iphlpapi.lib"/>
    <None Include="..\..\Plugins\BPFunctionLib\Source\NetConnection\NetConnection.Build.cs"/>
    <ClCompile Include="..\..\Plugins\BPFunctionLib\Source\NetConnection\Private\NetConnectionModule.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\BPFunctionLib\Intermediate\Build\Win64\UE4Editor\Development\NetConnection\Definitions.NetConnection.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\BPFunctionLib\Source\NetConnection\Private\TcpSocket.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\BPFunctionLib\Intermediate\Build\Win64\UE4Editor\Development\NetConnection\Definitions.NetConnection.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\BPFunctionLib\Source\NetConnection\Public\NetConnectionModule.h"/>
    <ClInclude Include="..\..\Plugins\BPFunctionLib\Source\NetConnection\Public\TcpSocket.h"/>
    <None Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\JsonLibrary.Build.cs"/>
    <ClCompile Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Private\JsonLibraryBlueprintHelpers.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Development\JsonLibrary\Definitions.JsonLibrary.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Private\JsonLibraryConverter.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Development\JsonLibrary\Definitions.JsonLibrary.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Private\JsonLibraryHelpers.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Development\JsonLibrary\Definitions.JsonLibrary.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Private\JsonLibraryList.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Development\JsonLibrary\Definitions.JsonLibrary.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Private\JsonLibraryModule.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Development\JsonLibrary\Definitions.JsonLibrary.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Private\JsonLibraryObject.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Development\JsonLibrary\Definitions.JsonLibrary.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Private\JsonLibraryValue.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Development\JsonLibrary\Definitions.JsonLibrary.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Public\JsonLibrary.h"/>
    <ClInclude Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Public\JsonLibraryBlueprintHelpers.h"/>
    <ClInclude Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Public\JsonLibraryConverter.h"/>
    <ClInclude Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Public\JsonLibraryEnums.h"/>
    <ClInclude Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Public\JsonLibraryHelpers.h"/>
    <ClInclude Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Public\JsonLibraryList.h"/>
    <ClInclude Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Public\JsonLibraryModule.h"/>
    <ClInclude Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Public\JsonLibraryObject.h"/>
    <ClInclude Include="..\..\Plugins\JsonLibrary\Source\JsonLibrary\Public\JsonLibraryValue.h"/>
    <None Include="..\..\Plugins\JsonLibrary\Source\JsonLibraryBlueprintSupport\JsonLibraryBlueprintSupport.Build.cs"/>
    <ClCompile Include="..\..\Plugins\JsonLibrary\Source\JsonLibraryBlueprintSupport\Private\JsonLibraryBlueprintSupportModule.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Development\JsonLibraryBlueprintSupport\Definitions.JsonLibraryBlueprintSupport.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\UnrealEd\SharedPCH.UnrealEd.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\JsonLibrary\Source\JsonLibraryBlueprintSupport\Private\K2Node_JsonLibraryFromStruct.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Development\JsonLibraryBlueprintSupport\Definitions.JsonLibraryBlueprintSupport.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\UnrealEd\SharedPCH.UnrealEd.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\JsonLibrary\Source\JsonLibraryBlueprintSupport\Private\K2Node_JsonLibraryToStruct.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\JsonLibrary\Intermediate\Build\Win64\UE4Editor\Development\JsonLibraryBlueprintSupport\Definitions.JsonLibraryBlueprintSupport.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\UnrealEd\SharedPCH.UnrealEd.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\JsonLibrary\Source\JsonLibraryBlueprintSupport\Public\K2Node_JsonLibraryFromStruct.h"/>
    <ClInclude Include="..\..\Plugins\JsonLibrary\Source\JsonLibraryBlueprintSupport\Public\K2Node_JsonLibraryToStruct.h"/>
    <None Include="..\..\Plugins\WebView\Source\WebView\WebView.Build.cs"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\SWebViewBrowser.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\SWebViewBrowserView.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\WebViewBrowser.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\WebViewBrowserAdapter.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\WebViewBrowserLog.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\WebViewBrowserSingleton.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\WebViewBrowserSingleton.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\WebViewBrowserViewport.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\WebViewJSFunction.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\WebViewJSScripting.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\WebViewModule.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFCookieManager.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewBrowserApp.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewBrowserApp.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewBrowserByteResource.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewBrowserByteResource.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewBrowserClosureTask.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewBrowserHandler.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewBrowserHandler.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewBrowserPopupFeatures.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewBrowserPopupFeatures.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewImeHandler.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewImeHandler.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewJSScripting.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewJSScripting.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewJSStructDeserializerBackend.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewJSStructDeserializerBackend.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewJSStructSerializerBackend.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewJSStructSerializerBackend.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewLibCefIncludes.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewResourceContextHandler.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewResourceContextHandler.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewSchemeHandler.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewSchemeHandler.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewTextInputMethodContext.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFViewTextInputMethodContext.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFWebViewBrowserDialog.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFWebViewBrowserWindow.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFWebViewBrowserWindow.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFWebViewBrowserWindowRHIHelper.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\CEF\CEFWebViewBrowserWindowRHIHelper.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\Native\NativeViewJSScripting.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\Native\NativeViewJSScripting.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\Native\NativeViewJSStructDeserializerBackend.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\Native\NativeViewJSStructDeserializerBackend.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\Native\NativeViewJSStructSerializerBackend.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\Native\NativeViewJSStructSerializerBackend.h"/>
    <ClCompile Include="..\..\Plugins\WebView\Source\WebView\Private\Native\NativeWebViewBrowserProxy.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\WebView\Source\ThirdParty\CEF3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Plugins\WebView\Intermediate\Build\Win64\UE4Editor\Development\WebView\Definitions.WebView.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Private\Native\NativeWebViewBrowserProxy.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\IWebViewBrowserAdapter.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\IWebViewBrowserCookieManager.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\IWebViewBrowserDialog.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\IWebViewBrowserPopupFeatures.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\IWebViewBrowserResourceLoader.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\IWebViewBrowserSchemeHandler.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\IWebViewBrowserSingleton.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\IWebViewBrowserWindow.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\SWebViewBrowser.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\SWebViewBrowserView.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\WebViewBrowser.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\WebViewBrowserViewport.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\WebViewJSFunction.h"/>
    <ClInclude Include="..\..\Plugins\WebView\Source\WebView\Public\WebViewModule.h"/>
    <ClCompile Include="..\..\Source\YunnToCrypto\MyActor.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Intermediate\Build\Win64\UE4Editor\Development\YunnToCrypto\Definitions.YunnToCrypto.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Source\YunnToCrypto\MyActor.h"/>
    <None Include="..\..\Source\YunnToCrypto\YunnToCrypto.Build.cs"/>
    <ClCompile Include="..\..\Source\YunnToCrypto\YunnToCrypto.cpp">
      <AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\cudamanager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\filebuf;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\foundation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\pvd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PxShared\include\task;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\cooking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\PhysX_3.4\Include\geometry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\clothing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\nvparameterized;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\legacy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\include\PhysX3;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\common\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\framework\include\autogen;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\RenderDebug\public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\general\PairFilter\include;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\PhysX3\APEX_1.4\shared\internal\include;</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(NMakeForcedIncludes);$(SolutionDir)Intermediate\Build\Win64\UE4Editor\Development\YunnToCrypto\Definitions.YunnToCrypto.h;$(SolutionDir)Intermediate\Build\Win64\YunnToCryptoEditor\Development\Engine\SharedPCH.Engine.ShadowErrors.h</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Source\YunnToCrypto\YunnToCrypto.h"/>
  </ItemGroup>
  <PropertyGroup>
    <SourcePath>C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TreeMap;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AITestSuite\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AITestSuite\Private\Actions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AITestSuite\Private\BehaviorTree;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AITestSuite\Private\MockAI;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AITestSuite\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AllDesktopTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Android\AndroidDeviceDetection\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Android\AndroidPlatformEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Android\AndroidTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Apple\MetalShaderFormat\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AssetTools\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AssetTools\Private\AssetTypeActions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AssetTools\Private\AssetTypeActions\Experimental;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AudioFormatADPCM\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AudioFormatOgg\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AudioFormatOpus\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AudioSettingsEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AutomationController\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AutomationDriver\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AutomationDriver\Private\Locators;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AutomationDriver\Private\Specs;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\AutomationWindow\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\BlankModule\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\BlueprintCompilerCppBackend\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\BlueprintNativeCodeGen\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\CollectionManager\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\CollisionAnalyzer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\CrashDebugHelper\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\CrashDebugHelper\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\CrashDebugHelper\Private\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\CrashDebugHelper\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\CrashDebugHelper\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\CrashDebugHelper\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Datasmith\DatasmithExporter\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Datasmith\DatasmithExporterUI\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Datasmith\DatasmithExporterUI\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Datasmith\DatasmithFacade\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DerivedDataCache\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DesktopPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DesktopPlatform\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DesktopPlatform\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DesktopPlatform\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DesktopWidgets\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DesktopWidgets\Private\Widgets\Input;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DeviceManager\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DeviceManager\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DeviceManager\Private\Widgets\Apps;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DeviceManager\Private\Widgets\Browser;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DeviceManager\Private\Widgets\Details;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DeviceManager\Private\Widgets\Processes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DeviceManager\Private\Widgets\Toolbar;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DirectoryWatcher\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DirectoryWatcher\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DirectoryWatcher\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DirectoryWatcher\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\DirectoryWatcher\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\EditorAnalyticsSession\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ExternalImagePicker\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\FileUtilities\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\FunctionalTesting\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\FunctionalTesting\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\GameplayDebugger\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\GameplayDebugger\Private\Editor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\GammaUI\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\GraphColor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\HierarchicalLODUtilities\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\HotReload\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\IOS\IOSPlatformEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\IOS\IOSTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\IOS\IOSTargetPlatform\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\IOS\IOSTargetPlatform\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\IOS\TVOSTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\IoStoreUtilities\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\LauncherServices\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\LauncherServices\Private\Launcher;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\LauncherServices\Private\Profiles;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Linux\LinuxAArch64ClientTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Linux\LinuxAArch64NoEditorTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Linux\LinuxAArch64ServerTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Linux\LinuxClientTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Linux\LinuxNoEditorTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Linux\LinuxPlatformEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Linux\LinuxServerTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Linux\LinuxTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Localization\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Localization\Private\Serialization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\LocalizationService\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\LogVisualizer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Lumin\LuminPlatformEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Lumin\LuminTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Mac\MacClientTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Mac\MacNoEditorTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Mac\MacPlatformEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Mac\MacServerTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Mac\MacTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MaterialBaking\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MaterialUtilities\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Merge\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshBoneReduction\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshBuilder\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshBuilderCommon\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshDescriptionOperations\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshMergeUtilities\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshReductionInterface\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshSimplifier\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MeshUtilities\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MessageLog\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MessageLog\Private\Model;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MessageLog\Private\Presentation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\MessageLog\Private\UserInterface;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ModuleUI\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\OutputLog\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\PakFileUtilities\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Profiler\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Profiler\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProfilerClient\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProfilerMessages\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProfilerService\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Archive;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Build;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Cook;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Deploy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Launch;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Package;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Preview;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Profile;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Progress;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Project;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Settings;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Shared;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\RealtimeProfiler\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\RigVMDeveloper\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\RigVMDeveloper\Private\RigVMCompiler;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\RigVMDeveloper\Private\RigVMModel;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\RigVMDeveloper\Private\RigVMModel\Nodes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ScreenShotComparison\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ScreenShotComparison\Private\Models;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ScreenShotComparison\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ScreenShotComparisonTools\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ScriptDisassembler\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SessionFrontend\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SessionFrontend\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SessionFrontend\Private\Widgets\Browser;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SessionFrontend\Private\Widgets\Console;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Settings\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SettingsEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SettingsEditor\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ShaderCompilerCommon\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ShaderFormatOpenGL\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ShaderFormatVectorVM\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ShaderPreprocessor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SharedSettingsWidgets\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SkeletalMeshUtilitiesCommon\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SlackIntegrations\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SlateFileDialogs\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SlateReflector\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SlateReflector\Private\Models;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SlateReflector\Private\Styling;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SlateReflector\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SourceCodeAccess\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\SourceControl\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\StandaloneRenderer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\StandaloneRenderer\Private\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\StandaloneRenderer\Private\OpenGL;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\StandaloneRenderer\Private\Linux\OpenGL;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\StandaloneRenderer\Private\Mac\OpenGL;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\StandaloneRenderer\Private\Windows\D3D;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\StandaloneRenderer\Private\Windows\OpenGL;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TargetDeviceServices\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TargetDeviceServices\Private\Proxies;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TargetDeviceServices\Private\Services;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TaskGraph\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TextureCompressor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TextureFormatASTC\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TextureFormatDXT\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TextureFormatETC2\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TextureFormatIntelISPCTexComp\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TextureFormatPVR\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TextureFormatUncompressed\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\ToolMenus\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceAnalysis\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceAnalysis\Private\Analysis;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceAnalysis\Private\Asio;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceAnalysis\Private\Store;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceAnalysis\Private\Analysis\Transport;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\Common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\LoadingProfiler;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\MemoryProfiler;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\NetworkingProfiler;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\StoreService;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\ViewModels;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\LoadingProfiler\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\MemoryProfiler\ViewModels;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\MemoryProfiler\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\NetworkingProfiler\ViewModels;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\NetworkingProfiler\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\Table\ViewModels;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceInsights\Private\Insights\Table\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceServices\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceServices\Private\Analyzers;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceServices\Private\Common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceServices\Private\Model;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\TraceServices\Private\Modules;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\VulkanShaderFormat\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Windows\LiveCoding\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Windows\LiveCoding\Private\External;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Windows\LiveCodingServer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Windows\LiveCodingServer\Private\External;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Windows\ShaderFormatD3D\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Windows\WindowsClientTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Windows\WindowsNoEditorTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Windows\WindowsPlatformEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Windows\WindowsServerTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\Windows\WindowsTargetPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Developer\XGEController\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UATHelper;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ViewportInteraction;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\VREditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ActorPickerMode\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AddContentDialog\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AddContentDialog\Private\ViewModels;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AddContentDialog\Private\ContentSourceProviders\FeaturePack;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AdvancedPreviewScene\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AIGraph\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AnimationBlueprintEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AnimationBlueprintEditor\Private\AnimationNodes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AnimationBlueprintEditor\Private\AnimationPins;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AnimationBlueprintEditor\Private\AnimationStateNodes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AnimationEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AnimationModifiers\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AnimGraph\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AnimGraph\Private\EditModes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AssetTagsEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AudioEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AudioEditor\Private\AssetTypeActions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AudioEditor\Private\Editors;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\AudioEditor\Private\Factories;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\BehaviorTreeEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\BehaviorTreeEditor\Private\DetailCustomizations;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\BlueprintGraph\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Blutility\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Cascade\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Cascade\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ClassViewer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ClothingSystemEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ClothingSystemEditorInterface\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ClothPainter\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\CommonMenuExtensions\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ComponentVisualizers\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ConfigEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ConfigEditor\Private\PropertyVisualization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ContentBrowser\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ContentBrowserData\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\CurveAssetEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\CurveEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\CurveEditor\Private\DragOperations;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\CurveEditor\Private\Filters;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\CurveEditor\Private\Tree;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\CurveEditor\Private\Views;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\CurveEditor\Public\Filters;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\CurveTableEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\DataTableEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\DetailCustomizations\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\DeviceProfileEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\DeviceProfileEditor\Private\DetailsPanel;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\DeviceProfileServices\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\DistCurveEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Documentation\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\EditorSettingsViewer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\EditorStyle\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\EditorSubsystem\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\EditorWidgets\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\EnvironmentLightingViewer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\FoliageEdit\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\FontEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\GameplayTasksEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\GameProjectGeneration\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\GameProjectGeneration\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\GraphEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\GraphEditor\Private\KismetNodes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\GraphEditor\Private\KismetPins;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\GraphEditor\Private\MaterialNodes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\GraphEditor\Private\MaterialPins;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\GraphEditor\Private\NiagaraPins;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\HardwareTargeting\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\HierarchicalLODOutliner\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\InputBindingEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\InputBindingEditor\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\InternationalizationSettings\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\IntroTutorials\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Kismet\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Kismet\Private\Debugging;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Kismet\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Kismet\Private\WorkflowOrientedApp;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\KismetCompiler\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\KismetWidgets\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\LandscapeEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\LandscapeEditor\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\LandscapeEditorUtilities\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Layers\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\LevelEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\LocalizationCommandletExecution\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\LocalizationDashboard\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MainFrame\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MainFrame\Private\Frame;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MainFrame\Private\Menus;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MaterialEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MaterialEditor\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Matinee\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MergeActors\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MergeActors\Private\MergeProxyUtils;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MergeActors\Private\MeshInstancingTool;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MergeActors\Private\MeshMergingTool;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MergeActors\Private\MeshProxyTool;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MeshPaint\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MeshPaintMode\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MovieSceneCaptureDialog\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MovieSceneTools\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MovieSceneTools\Private\Channels;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MovieSceneTools\Private\CurveKeyEditors;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MovieSceneTools\Private\EditModes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MovieSceneTools\Private\FCPXML;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MovieSceneTools\Private\Sections;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MovieSceneTools\Private\TrackEditors;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MovieSceneTools\Private\TrackEditorThumbnail;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\MovieSceneTools\Private\TrackEditors\PropertyTrackEditors;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\NewLevelDialog\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\OverlayEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\OverlayEditor\Private\Factories;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PackagesDialog\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Persona\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Persona\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Persona\Private\AnimTimeline;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Persona\Private\Customization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Persona\Private\EditModes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Persona\Private\Shared;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PhysicsAssetEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PhysicsAssetEditor\Private\PhysicsAssetGraph;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PIEPreviewDeviceProfileSelector\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PIEPreviewDeviceSpecification\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PinnedCommandList\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PixelInspector\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PlacementMode\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PListEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PluginWarden\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ProjectSettingsViewer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ProjectTargetPlatformEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ProjectTargetPlatformEditor\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PropertyEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PropertyEditor\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PropertyEditor\Private\Presentation\PropertyEditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PropertyEditor\Private\Presentation\PropertyTable;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PropertyEditor\Private\UserInterface\PropertyDetails;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PropertyEditor\Private\UserInterface\PropertyEditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\PropertyEditor\Private\UserInterface\PropertyTable;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SceneDepthPickerMode\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SceneOutliner\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Sequencer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Sequencer\Private\DisplayNodes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\Sequencer\Private\Tools;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SequenceRecorder\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SequenceRecorder\Private\Sections;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SequenceRecorderSections\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SequencerWidgets\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SerializedRecorderInterface\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SkeletalMeshEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SkeletonEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SourceControlWindows\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\StaticMeshEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\StatsViewer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\StatsViewer\Private\StatsEntries;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\StatsViewer\Private\StatsPages;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\StringTableEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\StructViewer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\SwarmInterface\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\TextureEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\TextureEditor\Private\Customizations;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\TextureEditor\Private\Models;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\TextureEditor\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\TimeManagementEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ToolMenusEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\TranslationEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Classes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Animation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\BlueprintModes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Components;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Customizations;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Designer;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Details;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\DragDrop;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Extensions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Hierarchy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Navigation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Nodes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Palette;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Settings;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\TabFactory;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Templates;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Utility;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UMGEditor\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UndoHistory\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UndoHistory\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Analytics;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Animation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\AutoReimport;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Bookmarks;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Commandlets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Cooker;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Dialogs;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\DragAndDrop;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Editor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Factories;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Fbx;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Features;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Kismet2;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Layers;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Lightmass;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\MaterialEditor;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Settings;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\StaticLightingSystem;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Subsystems;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\ThumbnailRendering;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Toolkits;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Tools;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEd\Private\Factories\Experimental;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\UnrealEdMessages\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ViewportInteraction\Gizmo;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\ViewportSnapping\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\VirtualTexturingEditor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\VREditor\Teleporter;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\VREditor\UI;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\WorkspaceMenuStructure\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\WorldBrowser\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\WorldBrowser\Private\StreamingLevels;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Editor\WorldBrowser\Private\Tiles;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Programs\UnrealHeaderTool\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Programs\UnrealHeaderTool\Private\Specifiers;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Advertising\Advertising\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Advertising\Android\AndroidAdvertising\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Advertising\IOS\IOSAdvertising\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\Actions;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\BehaviorTree;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\Blueprint;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\DataProviders;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\GameplayDebugger;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\HotSpots;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\Navigation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\Perception;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\Tasks;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Blackboard;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Composites;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Decorators;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Services;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Tasks;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Contexts;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Generators;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Items;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ALAudio\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Analytics\Analytics\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Analytics\AnalyticsET\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Analytics\AnalyticsSwrve\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Analytics\AnalyticsVisualEditing\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Android\AndroidAudio\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Android\AndroidLocalNotification\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Android\AndroidRuntimeSettings\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Android\AudioMixerAndroid\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AnimationCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AnimGraphRuntime\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AnimGraphRuntime\Private\AnimNodes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AnimGraphRuntime\Private\BoneControllers;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AnimGraphRuntime\Private\RBF;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AnimGraphRuntime\Public\AnimNodes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AppFramework\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AppFramework\Private\Framework\Testing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AppFramework\Private\Widgets\Colors;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AppFramework\Private\Widgets\Testing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AppFramework\Private\Widgets\Workflow;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Apple\AudioMixerAudioUnit\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Apple\AudioMixerCoreAudio\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Apple\MetalRHI\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Apple\MetalRHI\Private\Shaders;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Apple\MetalRHI\Private\Shaders\Debugging;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Apple\MetalRHI\Private\Shaders\Types;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\GenericPlatform;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\HAL;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\Lumin;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\Unix;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\GenericPlatform\Accessibility;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\IOS\Accessibility;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\Mac\Accessibility;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ApplicationCore\Private\Windows\Accessibility;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AssetRegistry\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioAnalyzer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioCaptureCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioCaptureImplementations\Android\AudioCaptureAndroid\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioCaptureImplementations\AudioCaptureRtAudio\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioCaptureImplementations\IOS\AudioCaptureAudioUnit\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioExtensions\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioMixer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioMixer\Private\Components;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioMixer\Private\Effects;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioMixer\Private\Generators;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioMixer\Private\Quartz;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioMixer\Private\SoundFileIO;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioMixerCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AudioPlatformConfiguration\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AugmentedReality\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AutomationMessages\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AutomationWorker\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AVEncoder\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AVEncoder\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AVEncoder\Private\Microsoft;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AVEncoder\Private\Microsoft\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\AVIWriter\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\BlueprintRuntime\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\BuildSettings\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Cbor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Cbor\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CEF3Utils\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CEF3Utils\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CinematicCamera\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ClientPilot\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ClothingSystemRuntimeCommon\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ClothingSystemRuntimeCommon\Private\Utils;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ClothingSystemRuntimeInterface\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ClothingSystemRuntimeNv\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CookedIterativeFile\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Apple;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Async;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Compression;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Containers;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Delegates;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Features;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\FileCache;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\FramePro;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\GenericPlatform;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\HAL;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Hash;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Internationalization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\IO;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Logging;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Lumin;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Math;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Memory;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\MemPro;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Misc;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Modules;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\ProfilingDebugging;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Serialization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Stats;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\String;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Templates;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Unix;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\UObject;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Containers\Algo;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\HAL\Allocators;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Internationalization\Cultures;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\ProfilingDebugging\Apple;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\ProfilingDebugging\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Serialization\Csv;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Serialization\Formatters;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Tests\Algo;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Tests\Async;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Tests\Containers;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Tests\HAL;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Tests\Internationalization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Tests\IO;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Tests\Math;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Tests\Misc;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Tests\Serialization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Tests\String;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Core\Private\Tests\Templates;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CoreUObject\Private\AssetRegistry;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CoreUObject\Private\Blueprint;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CoreUObject\Private\Internationalization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CoreUObject\Private\Misc;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CoreUObject\Private\Serialization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CoreUObject\Private\Templates;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CoreUObject\Private\UObject;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CoreUObject\Private\Serialization\Formatters;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CoreUObject\Private\Tests\Serialization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CoreUObject\Private\UObject\SavePackage;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CrashReportCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CrashReportCore\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CrashReportCore\Private\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CrashReportCore\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CrashReportCore\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\CrunchCompression\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\D3D12RHI\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\D3D12RHI\Private\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\D3D12RHI\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Datasmith\DatasmithCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Datasmith\DatasmithCore\Private\DirectLink;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Datasmith\DirectLink\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\DeveloperSettings\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\DeveloperSettings\Private\Engine;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\EmptyRHI\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Classes\Engine;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Classes\Sound;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\AI;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Analytics;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Animation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Atmosphere;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Audio;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Camera;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Collision;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Commandlets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Components;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Curves;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Debug;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\DeviceProfiles;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\EdGraph;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\EditorFramework;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Engine;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\GameFramework;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\HLOD;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Internationalization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Kismet;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Layers;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Materials;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\MeshVertexPainter;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Net;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\PacketHandlers;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Particles;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Performance;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\PhysicsEngine;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\ProfilingDebugging;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Rendering;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\ShaderCompiler;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Slate;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Streaming;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Subsystems;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\UserInterface;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Vehicles;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\VisualLogger;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\VT;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\AI\Navigation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\GameFramework\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Net\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Net\Tests\Util;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\PhysicsEngine\Experimental;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\PhysicsEngine\ImmediatePhysics;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\PhysicsEngine\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\PhysicsEngine\ImmediatePhysics\ImmediatePhysicsChaos;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\PhysicsEngine\ImmediatePhysics\ImmediatePhysicsPhysX;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Private\Tests\Internationalization;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Engine\Public\Rendering;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\EngineMessages\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\EngineSettings\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Private\ChaosVisualDebugger;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Private\Field;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Private\Framework;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Private\GeometryCollection;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Private\PhysicsProxy;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Collision;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Evolution;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Framework;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Joint;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\ChaosCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\ChaosSolverEngine\Private\Chaos;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesEngine\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\FieldSystem\Source\FieldSystemEngine\Private\Field;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\GeometryCollectionEngine\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\GeometryCollectionEngine\Private\GeometryCollection;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\InteractiveToolsFramework\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\InteractiveToolsFramework\Private\BaseBehaviors;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\InteractiveToolsFramework\Private\BaseGizmos;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\InteractiveToolsFramework\Private\BaseTools;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\InteractiveToolsFramework\Private\Changes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Experimental\Voronoi\Private\Voronoi;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ExternalRPCRegistry\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\EyeTracker\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Foliage\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\FriendsAndChat\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\GameMenuBuilder\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\GameplayMediaEncoder\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\GameplayTags\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\GameplayTags\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\GameplayTasks\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\GameplayTasks\Private\Tasks;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\HardwareSurvey\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\HeadMountedDisplay\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\IESFile\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ImageCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ImageWrapper\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ImageWrapper\Private\Formats;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\ImageWriteQueue\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputCore\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputCore\Private\GenericPlatform;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputCore\Private\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputCore\Private\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputCore\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputCore\Private\Lumin;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputCore\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputCore\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InputDevice\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\InstallBundleManager\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\IOS\IOSAudio\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\IOS\IOSLocalNotification\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\IOS\IOSPlatformFeatures\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\IOS\IOSRuntimeSettings\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\IOS\LaunchDaemonMessages\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\IPC\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Json\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Json\Private\Dom;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Json\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\JsonUtilities\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Landscape\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Landscape\Private\Materials;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Launch\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Launch\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Launch\Private\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Launch\Private\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Launch\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Launch\Private\Lumin;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Launch\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Launch\Private\Unix;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Launch\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\LevelSequence\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Linux\AudioMixerSDL\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\LiveLinkInterface\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\LiveLinkInterface\Private\Roles;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\LiveLinkMessageBusFramework\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Lumin\LuminRuntimeSettings\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Mac\CoreAudio\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Mac\UnrealAudioCoreAudio\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MaterialShaderQualitySettings\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Media\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MediaAssets\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MediaAssets\Private\Assets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MediaAssets\Private\Misc;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MediaAssets\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MediaInfo\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MediaUtils\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MeshDescription\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MeshDescription\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MeshUtilitiesCommon\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Messaging\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Messaging\Private\Bridge;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Messaging\Private\Bus;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MessagingCommon\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MessagingRpc\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MoviePlayer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private\Channels;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private\Compilation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private\EntitySystem;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private\Evaluation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private\Generators;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private\Sections;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private\Tracks;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private\EntitySystem\TrackInstance;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private\Evaluation\Blending;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieScene\Private\Evaluation\Instances;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieSceneCapture\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieSceneTracks\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieSceneTracks\Private\Channels;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieSceneTracks\Private\Evaluation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieSceneTracks\Private\Sections;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieSceneTracks\Private\Systems;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieSceneTracks\Private\TrackInstances;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieSceneTracks\Private\Tracks;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MovieSceneTracks\Private\EntitySystem\Interrogation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MRMesh\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\MRMesh\Public;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NavigationSystem\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NavigationSystem\Private\NavAreas;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NavigationSystem\Private\NavFilters;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NavigationSystem\Private\NavGraph;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NavigationSystem\Private\NavMesh;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NavigationSystem\Public\NavMesh;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Navmesh\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Navmesh\Private\DebugUtils;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Navmesh\Private\Detour;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Navmesh\Private\DetourCrowd;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Navmesh\Private\DetourTileCache;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Navmesh\Private\Recast;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Net\Common\Private\Net\Common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Net\Core\Private\Net\Core;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Net\Core\Private\Net\Core\Analytics;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Net\Core\Private\Net\Core\Misc;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Net\Core\Private\Net\Core\PushModel;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Net\Core\Private\Net\Core\Trace;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Net\Core\Private\Net\Core\Trace\Reporters;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NetworkFile\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NetworkFileSystem\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Networking\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Networking\Private\IPv4;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Networking\Private\Steam;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Networking\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NetworkReplayStreaming\HttpNetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NetworkReplayStreaming\InMemoryNetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NetworkReplayStreaming\LocalFileNetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NetworkReplayStreaming\NetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NetworkReplayStreaming\NullNetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NetworkReplayStreaming\SaveGameNetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NonRealtimeAudioRenderer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NullDrv\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\NullInstallBundleManager\Source;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BackgroundHTTP\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BackgroundHTTP\Private\GenericPlatform;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BackgroundHTTP\Private\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BackgroundHTTPFileHash\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BuildPatchServices\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BuildPatchServices\Private\Common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BuildPatchServices\Private\Compactify;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BuildPatchServices\Private\Core;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BuildPatchServices\Private\Data;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BuildPatchServices\Private\Diffing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BuildPatchServices\Private\Enumeration;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BuildPatchServices\Private\Generation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BuildPatchServices\Private\Installer;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BuildPatchServices\Private\Installer\Statistics;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\BuildPatchServices\Private\Tests\Unit;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Private\Apple;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Private\Curl;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Private\GenericPlatform;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Private\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Private\IXML;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Private\Unix;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Private\WinHttp;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTP\Private\WinHttp\Support;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTPServer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\HTTPServer\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\ICMP\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\ICMP\Private\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\ICMP\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\ImageDownload\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\SSL\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\SSL\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\SSL\Private\Unix;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\SSL\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\Stomp\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\Voice\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\Voice\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\Voice\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\Voice\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\Voice\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\WebSockets\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\WebSockets\Private\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\WebSockets\Private\Lws;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\WebSockets\Private\WinHttp;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\WebSockets\Private\WinHttp\Support;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\XMPP\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\XMPP\Private\XmppJingle;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Online\XMPP\Private\XmppStrophe;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\OpenGLDrv\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\OpenGLDrv\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\OpenGLDrv\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\OpenGLDrv\Private\Lumin;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\OpenGLDrv\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Overlay\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Overlay\Private\Assets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Overlay\Private\Factories;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\AsymmetricEncryption\RSAEncryptionHandlerComponent\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\EncryptionHandlerComponent\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\RSAKeyAESEncryption\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\AESBlockEncryptor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\BlockEncryptionHandlerComponent\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\BlowFishBlockEncryptor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\TwoFishBlockEncryptor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\XORBlockEncryptor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\StreamEncryption\StreamEncryptionHandlerComponent\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\StreamEncryption\XORStreamEncryptor\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\PacketHandler\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PacketHandlers\ReliabilityHandlerComponent\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PakFile\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PerfCounters\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PhysicsCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PhysXCooking\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Portal\LauncherCheck\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Portal\LauncherPlatform\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Portal\LauncherPlatform\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Portal\LauncherPlatform\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Portal\LauncherPlatform\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Portal\Messages\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Portal\Proxies\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Portal\Proxies\Private\Account;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Portal\Proxies\Private\Application;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Portal\Rpc\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Portal\Services\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PreLoadScreen\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Projects\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PropertyAccess\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\PropertyPath\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RawMesh\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RemoteImportMessaging\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RenderCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RenderCore\Private\ProfilingDebugging;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Renderer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Renderer\Private\CompositionLighting;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Renderer\Private\HairStrands;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Renderer\Private\PathTracing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Renderer\Private\PostProcess;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Renderer\Private\RayTracing;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Renderer\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Renderer\Private\ViewMode;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Renderer\Private\VT;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RHI\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RHI\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RHI\Private\Apple;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RHI\Private\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RHI\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RHI\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RigVM\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RigVM\Private\RigVMCore;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RSA\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\RuntimeAssetCache\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SandboxFile\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Serialization\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Serialization\Private\Backends;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Serialization\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SessionMessages\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SessionServices\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SignalProcessing\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\Application;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\Commands;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\Docking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\Layout;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\MetaData;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\MultiBox;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\Notifications;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\Styling;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\Text;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\MultiBox\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\Text\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\Text\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Framework\Text\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Widgets\Accessibility;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Widgets\Colors;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Widgets\Docking;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Widgets\Images;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Widgets\Input;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Widgets\LayerManager;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Widgets\Layout;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Widgets\Notifications;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Widgets\Text;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Slate\Private\Widgets\Views;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Animation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Application;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Brushes;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Debugging;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\FastUpdate;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Fonts;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Input;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Layout;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Rendering;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Sound;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Styling;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Textures;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Trace;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Types;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Widgets\Accessibility;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateCore\Private\Widgets\Images;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateNullRenderer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SlateRHIRenderer\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Sockets\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Sockets\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Sockets\Private\BSDSockets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Sockets\Private\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Sockets\Private\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Sockets\Private\Mac;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Sockets\Private\Unix;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Sockets\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SoundFieldRendering\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\StaticMeshDescription\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\StreamingFile\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\StreamingPauseRendering\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\SynthBenchmark\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\TimeManagement\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\TimeManagement\Private\Widgets;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Toolbox\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\TraceLog\Private\Trace;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Apple;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Unix;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\UE4Game\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\UMG\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\UMG\Private\Animation;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\UMG\Private\Binding;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\UMG\Private\Blueprint;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\UMG\Private\Components;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\UMG\Private\Slate;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Unix\UnixCommonStartup\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\UnrealAudio\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\UnrealAudio\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\VectorVM\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\VectorVM\Private\Tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\VirtualProduction\StageDataCore\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\VulkanRHI\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\VulkanRHI\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\VulkanRHI\Private\Linux;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\VulkanRHI\Private\Lumin;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\VulkanRHI\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\WebBrowser\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\WebBrowser\Private\Android;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\WebBrowser\Private\CEF;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\WebBrowser\Private\IOS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\WebBrowser\Private\MobileJS;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\WebBrowser\Private\Native;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\WebBrowserTexture\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\WidgetCarousel\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Windows\AudioMixerXAudio2\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Windows\D3D11RHI\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Windows\D3D11RHI\Private\HoloLens;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Windows\D3D11RHI\Private\Windows;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Windows\UnrealAudioWasapi\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Windows\UnrealAudioXAudio2\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Windows\WindowsPlatformFeatures\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\Windows\XAudio2\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\Runtime\XmlParser\Private;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\Android\detex;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\Android\libunwind\libunwind\android\tests;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\src\device_info\core;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\src\device_info\jni;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\src\swappy\common;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\src\swappy\opengl;C:\Program Files\Epic Games\UE_4.26\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\src\swappy\vulkan;</SourcePath>
  </PropertyGroup>
  <ItemDefinitionGroup>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <PropertyGroup>
    <CleanDependsOn> $(CleanDependsOn); </CleanDependsOn>
    <CppCleanDependsOn></CppCleanDependsOn>
  </PropertyGroup>
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
