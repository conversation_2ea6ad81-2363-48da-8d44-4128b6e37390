

[/Script/EngineSettings.GeneralProjectSettings]
ProjectID=80AABD3340F7AB55B943709BE4219212

[/Script/UnrealEd.ProjectPackagingSettings]
Build=IfProjectHasCode
BuildConfiguration=PPBC_Development
BuildTarget=
StagingDirectory=(Path="")
FullRebuild=False
ForDistribution=False
IncludeDebugFiles=False
BlueprintNativizationMethod=Disabled
bIncludeNativizedAssetsInProjectGeneration=False
bExcludeMonolithicEngineHeadersInNativizedCode=False
UsePakFile=True
bUseIoStore=False
bGenerateChunks=False
bGenerateNoChunks=False
bChunkHardReferencesOnly=False
bForceOneChunkPerFile=False
MaxChunkSize=0
bBuildHttpChunkInstallData=False
HttpChunkInstallDataDirectory=(Path="")
PakFileCompressionFormats=
PakFileAdditionalCompressionOptions=
HttpChunkInstallDataVersion=
IncludePrerequisites=False
IncludeAppLocalPrerequisites=False
bShareMaterialShaderCode=True
bDeterministicShaderCodeOrder=False
bSharedMaterialNativeLibraries=True
ApplocalPrerequisitesDirectory=(Path="")
IncludeCrashReporter=False
InternationalizationPreset=English
-CulturesToStage=en
+CulturesToStage=en
LocalizationTargetCatchAllChunkId=0
bCookAll=False
bCookMapsOnly=False
bCompressed=True
bSkipEditorContent=False
bSkipMovies=False
-IniKeyBlacklist=KeyStorePassword
-IniKeyBlacklist=KeyPassword
-IniKeyBlacklist=rsa.privateexp
-IniKeyBlacklist=rsa.modulus
-IniKeyBlacklist=rsa.publicexp
-IniKeyBlacklist=aes.key
-IniKeyBlacklist=SigningPublicExponent
-IniKeyBlacklist=SigningModulus
-IniKeyBlacklist=SigningPrivateExponent
-IniKeyBlacklist=EncryptionKey
-IniKeyBlacklist=IniKeyBlacklist
-IniKeyBlacklist=IniSectionBlacklist
+IniKeyBlacklist=KeyStorePassword
+IniKeyBlacklist=KeyPassword
+IniKeyBlacklist=rsa.privateexp
+IniKeyBlacklist=rsa.modulus
+IniKeyBlacklist=rsa.publicexp
+IniKeyBlacklist=aes.key
+IniKeyBlacklist=SigningPublicExponent
+IniKeyBlacklist=SigningModulus
+IniKeyBlacklist=SigningPrivateExponent
+IniKeyBlacklist=EncryptionKey
+IniKeyBlacklist=IniKeyBlacklist
+IniKeyBlacklist=IniSectionBlacklist
+MapsToCook=(FilePath="/Game/Main")

