// Copyright XiaoXin.  Year : 2021

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleInterface.h"
#include "Modules/ModuleManager.h"

class IWebViewBrowserSingleton;

struct FWebViewBrowserInitSettings
{
public:
	FWebViewBrowserInitSettings();
	FString ProductVersion;
};


class IWebViewBrowserModule : public IModuleInterface
{
public:
	static inline IWebViewBrowserModule& Get()
	{
		return FModuleManager::LoadModuleChecked<IWebViewBrowserModule>("WebView");
	}
	static inline bool IsAvailable()
	{
		return FModuleManager::Get().IsModuleLoaded("WebView");
	}

	virtual bool CustomInitialize(const FWebViewBrowserInitSettings& WebViewBrowserInitSettings) = 0;

	virtual IWebViewBrowserSingleton* GetSingleton() = 0;

	virtual bool IsWebModuleAvailable() const = 0;
};

class FWebViewModule : public IWebViewBrowserModule
{
public:
	FWebViewModule()
	{
		bWebViewModule = false;

		chrome_elf = nullptr;
		d3dcompiler_47 = nullptr;
		libcef = nullptr;
		libEGL = nullptr;
		libGLESv2 = nullptr;
		turbojpeg = nullptr;
	}

	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

public:
	virtual bool IsWebModuleAvailable() const override;
	virtual IWebViewBrowserSingleton* GetSingleton() override;
	virtual bool CustomInitialize(const FWebViewBrowserInitSettings& WebViewBrowserInitSettings) override;

private:
	bool bWebViewModule = false;

	void* chrome_elf = nullptr;
	void* d3dcompiler_47 = nullptr;
	void* libcef = nullptr;
	void* libEGL = nullptr;
	void* libGLESv2 = nullptr;
	void* turbojpeg = nullptr;
};