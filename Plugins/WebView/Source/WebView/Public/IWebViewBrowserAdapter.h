// Copyright XiaoXin.  Year : 2021

#pragma once

#include "CoreMinimal.h"
#include "IWebViewBrowserWindow.h"

class IWebViewBrowserAdapter
{
public:

	virtual FString GetName() const = 0;

	virtual bool IsPermanent() const = 0;

	virtual void ConnectTo(const TSharedRef<IWebViewBrowserWindow>& BrowserWindow) = 0;

	virtual void DisconnectFrom(const TSharedRef<IWebViewBrowserWindow>& BrowserWindow) = 0;

};

class FWebViewBrowserAdapterFactory 
{ 
public: 

	static TSharedRef<IWebViewBrowserAdapter> Create(const FString& Name, UObject* JSBridge, bool IsPermanent); 

	static TSharedRef<IWebViewBrowserAdapter> Create(const FString& Name, UObject* JSBridge, bool IsPermanent, const FString& ConnectScriptText, const FString& DisconnectScriptText);
}; 
