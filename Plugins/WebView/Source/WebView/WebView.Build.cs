// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;
using System.Collections.Generic;
using System.IO;

public class WebView : ModuleRules
{
	public WebView(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		
		PublicIncludePaths.AddRange(
			new string[] {
				// ... add public include paths required here ...
			}
			);
				
		
		PrivateIncludePaths.AddRange(
			new string[] {
				// ... add other private include paths required here ...
			}
			);
			
		
		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"Slate",
				"SlateCore",
				"HTTP",
				"CoreUObject",
				"Engine",
				"UMG",
				"Serialization",
				"RHI",
				"ApplicationCore",
				"InputCore",
				"RenderCore",
				"Projects",
				// ... add other public dependencies that you statically link with here ...
			}
			);
			
		
		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				// ... add private dependencies that you statically link with here ...	
			}
			);
		
		
		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
				// ... add any modules that your module loads dynamically here ...
			}
			);

		string ThirdPartyPath = Path.Combine(ModuleDirectory, "../ThirdParty/CEF3");
		PublicSystemIncludePaths.Add(ThirdPartyPath);

		PublicAdditionalLibraries.Add(Path.Combine(ThirdPartyPath, "lib/libcef.lib"));
		PublicAdditionalLibraries.Add(Path.Combine(ThirdPartyPath, "lib/libcef_dll_wrapper.lib"));
		
		List<string> Dlls = new List<string>();

		Dlls.Add("chrome_elf.dll");
		Dlls.Add("d3dcompiler_47.dll");
		Dlls.Add("libcef.dll");
		Dlls.Add("libEGL.dll");
		Dlls.Add("libGLESv2.dll");
		Dlls.Add("turbojpeg.dll");

		PublicDelayLoadDLLs.AddRange(Dlls);

		string dllPath = ThirdPartyPath + "/dll";
		dllPath = dllPath.Replace('\\', '/');

		foreach (string fileName in Directory.EnumerateFiles(ThirdPartyPath + "/dll", "*", SearchOption.AllDirectories))
		{
			string dependencyName = fileName.Replace('\\', '/');
			RuntimeDependencies.Add(dependencyName);

			FileInfo fileInfo = new FileInfo(dependencyName);

			string relativelyPath = dependencyName.Replace(dllPath, "");
			relativelyPath = relativelyPath.Replace(fileInfo.Name, "");
			RuntimeDependencies.Add(Path.Combine("$(BinaryOutputDir)" + relativelyPath + fileInfo.Name), dependencyName, StagedFileType.NonUFS);
		}
	}
}
