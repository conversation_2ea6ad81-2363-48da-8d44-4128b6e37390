// Copyright XiaoXin.  Year : 2022.

#include "WebViewModule.h"
#include "WebViewBrowserSingleton.h"
#include "Misc/App.h"
#include "Misc/EngineVersion.h"
#include "Modules/ModuleManager.h"
#include "Interfaces/IPluginManager.h"

#define LOCTEXT_NAMESPACE "FWebViewModule"

static FWebViewBrowserSingleton* WebViewBrowserSingleton = nullptr;

FWebViewBrowserInitSettings::FWebViewBrowserInitSettings()
	: ProductVersion(FString::Printf(TEXT("%s/%s UnrealEngine/%s Chrome/84.0.4147.38"), FApp::GetProjectName(), FApp::GetBuildVersion(), *FEngineVersion::Current().ToString()))
{

}

void FWebViewModule::StartupModule()
{
	const FString PluginDir = IPluginManager::Get().FindPlugin(TEXT("WebView"))->GetBaseDir();
	FString DllDir = FPaths::Combine(PluginDir, TEXT("Source/ThirdParty/CEF3/dll/"));
	DllDir = FPaths::ConvertRelativePathToFull(DllDir);

	chrome_elf = FPlatformProcess::GetDllHandle(*FPaths::Combine(DllDir,TEXT("chrome_elf.dll")));
	d3dcompiler_47 = FPlatformProcess::GetDllHandle(*FPaths::Combine(DllDir, TEXT("d3dcompiler_47.dll")));
	libcef = FPlatformProcess::GetDllHandle(*FPaths::Combine(DllDir, TEXT("libcef.dll")));
	libEGL = FPlatformProcess::GetDllHandle(*FPaths::Combine(DllDir, TEXT("libEGL.dll")));
	libGLESv2 = FPlatformProcess::GetDllHandle(*FPaths::Combine(DllDir, TEXT("libGLESv2.dll")));
	turbojpeg = FPlatformProcess::GetDllHandle(*FPaths::Combine(DllDir, TEXT("turbojpeg.dll")));

	bWebViewModule = true;
	FWebViewModule::Get().GetSingleton();	
}

void FWebViewModule::ShutdownModule()
{
	return;
	if (WebViewBrowserSingleton != nullptr)
	{
		system("taskkill /f /t /im UnrealCEFSubProcess.exe");
		delete WebViewBrowserSingleton;
		WebViewBrowserSingleton = nullptr;

		if (chrome_elf)
		{
			FPlatformProcess::FreeDllHandle(chrome_elf);
			chrome_elf = nullptr;
		}
		if (d3dcompiler_47)
		{
			FPlatformProcess::FreeDllHandle(d3dcompiler_47);
			d3dcompiler_47 = nullptr;
		}
		if (libcef)
		{
			FPlatformProcess::FreeDllHandle(libcef);
			libcef = nullptr;
		}
		if (libEGL)
		{
			FPlatformProcess::FreeDllHandle(libEGL);
			libEGL = nullptr;
		}
		if (libGLESv2)
		{
			FPlatformProcess::FreeDllHandle(libGLESv2);
			libGLESv2 = nullptr;
		}
		if (turbojpeg)
		{
			FPlatformProcess::FreeDllHandle(turbojpeg);
			turbojpeg = nullptr;
		}
	}
}

bool FWebViewModule::CustomInitialize(const FWebViewBrowserInitSettings& WebViewBrowserInitSettings)
{
	if (WebViewBrowserSingleton == nullptr)
	{
		WebViewBrowserSingleton = new FWebViewBrowserSingleton(WebViewBrowserInitSettings);

		return true;
	}
	return false;
}

IWebViewBrowserSingleton* FWebViewModule::GetSingleton()
{
	if (WebViewBrowserSingleton == nullptr)
	{
		WebViewBrowserSingleton = new FWebViewBrowserSingleton(FWebViewBrowserInitSettings());

		WebViewBrowserSingleton->DefaultMaterialPtr = FString(TEXT("/WebView/WebTexture_M.WebTexture_M"));
		WebViewBrowserSingleton->DefaultTranslucentMaterialPtr = FString(TEXT("/WebView/WebTexture_TM.WebTexture_TM"));

		WebViewBrowserSingleton->DefaultMaterialPtr.LoadSynchronous();
		WebViewBrowserSingleton->DefaultTranslucentMaterialPtr.LoadSynchronous();

		WebViewBrowserSingleton->SetDefaultMaterial(WebViewBrowserSingleton->DefaultMaterialPtr.Get());
		WebViewBrowserSingleton->SetDefaultTranslucentMaterial(WebViewBrowserSingleton->DefaultTranslucentMaterialPtr.Get());
	}
	return WebViewBrowserSingleton;
}


bool FWebViewModule::IsWebModuleAvailable() const
{
	return bWebViewModule;
}


#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FWebViewModule, WebView)