// Copyright XiaoXin.  Year : 2021

#include "WebViewBrowserViewport.h"
#include "Textures/SlateShaderResource.h"
#include "Widgets/SWidget.h"
#include "IWebViewBrowserWindow.h"
#include "Layout/WidgetPath.h"

#if WITH_CEF3
#include "CEF/CEFWebViewBrowserWindow.h"
#endif

FIntPoint FWebViewBrowserViewport::GetSize() const
{
	return (WebViewBrowserWindow->GetTexture(bIsPopup) != nullptr)
		? FIntPoint(WebViewBrowserWindow->GetTexture(bIsPopup)->GetWidth(), WebViewBrowserWindow->GetTexture(bIsPopup)->GetHeight())
		: FIntPoint();
}

FSlateShaderResource* FWebViewBrowserViewport::GetViewportRenderTargetTexture() const
{
	return WebViewBrowserWindow->GetTexture(bIsPopup);
}

void FWebViewBrowserViewport::Tick( const FGeometry& AllottedGeometry, double InCurrentTime, float DeltaTime )
{
	if (!bIsPopup)
	{
		const float DPI = (WebViewBrowserWindow->GetParentWindow().IsValid() ? WebViewBrowserWindow->GetParentWindow()->GetNativeWindow()->GetDPIScaleFactor() : 1.0f);
		const float DPIScale = AllottedGeometry.Scale / DPI;
		FVector2D AbsoluteSize = AllottedGeometry.GetLocalSize() * DPIScale;
		WebViewBrowserWindow->SetViewportSize(AbsoluteSize.IntPoint(), AllottedGeometry.GetAbsolutePosition().IntPoint());

#if WITH_CEF3
		// Forward the AllottedGeometry to the WebViewBrowserWindow so the IME implementation can use it
		TSharedPtr<FCEFWebViewBrowserWindow> CefWebViewBrowserWindow = StaticCastSharedPtr<FCEFWebViewBrowserWindow>(WebViewBrowserWindow);
		CefWebViewBrowserWindow->UpdateCachedGeometry(AllottedGeometry);
#endif
	}
}

bool FWebViewBrowserViewport::RequiresVsync() const
{
	return false;
}

FCursorReply FWebViewBrowserViewport::OnCursorQuery( const FGeometry& MyGeometry, const FPointerEvent& CursorEvent )
{
	return WebViewBrowserWindow->OnCursorQuery(MyGeometry, CursorEvent);
}

FReply FWebViewBrowserViewport::OnMouseButtonDown(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	// Capture mouse on left button down so that you can drag out of the viewport
	FReply Reply = WebViewBrowserWindow->OnMouseButtonDown(MyGeometry, MouseEvent, bIsPopup);
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		const FWidgetPath* Path = MouseEvent.GetEventPath();
		if (Path->IsValid())
		{
			TSharedRef<SWidget> TopWidget = Path->Widgets.Last().Widget;
			return Reply.CaptureMouse(TopWidget);
		}
	}
	return Reply;
}

FReply FWebViewBrowserViewport::OnMouseButtonUp(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	// Release mouse capture when left button released
	FReply Reply = WebViewBrowserWindow->OnMouseButtonUp(MyGeometry, MouseEvent, bIsPopup);
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		return Reply.ReleaseMouseCapture();
	}
	return Reply;
}

void FWebViewBrowserViewport::OnMouseEnter(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
}

void FWebViewBrowserViewport::OnMouseLeave(const FPointerEvent& MouseEvent)
{
	WebViewBrowserWindow->OnMouseLeave(MouseEvent);
}

FReply FWebViewBrowserViewport::OnMouseMove(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	return WebViewBrowserWindow->OnMouseMove(MyGeometry, MouseEvent, bIsPopup);
}

FReply FWebViewBrowserViewport::OnMouseWheel(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	return WebViewBrowserWindow->OnMouseWheel(MyGeometry, MouseEvent, bIsPopup);
}

FReply FWebViewBrowserViewport::OnMouseButtonDoubleClick(const FGeometry& InMyGeometry, const FPointerEvent& InMouseEvent)
{
	FReply Reply = WebViewBrowserWindow->OnMouseButtonDoubleClick(InMyGeometry, InMouseEvent, bIsPopup);
	return Reply;
}

FReply FWebViewBrowserViewport::OnKeyDown(const FGeometry& MyGeometry, const FKeyEvent& InKeyEvent)
{
	return WebViewBrowserWindow->OnKeyDown(InKeyEvent) ? FReply::Handled() : FReply::Unhandled();
}

FReply FWebViewBrowserViewport::OnKeyUp(const FGeometry& MyGeometry, const FKeyEvent& InKeyEvent)
{
	return WebViewBrowserWindow->OnKeyUp(InKeyEvent) ? FReply::Handled() : FReply::Unhandled();
}

FReply FWebViewBrowserViewport::OnKeyChar( const FGeometry& MyGeometry, const FCharacterEvent& InCharacterEvent )
{
	return WebViewBrowserWindow->OnKeyChar(InCharacterEvent) ? FReply::Handled() : FReply::Unhandled();
}

FReply FWebViewBrowserViewport::OnFocusReceived(const FFocusEvent& InFocusEvent)
{
	WebViewBrowserWindow->OnFocus(true, bIsPopup);
	return FReply::Handled();
}

void FWebViewBrowserViewport::OnFocusLost(const FFocusEvent& InFocusEvent)
{
	WebViewBrowserWindow->OnFocus(false, bIsPopup);
}
