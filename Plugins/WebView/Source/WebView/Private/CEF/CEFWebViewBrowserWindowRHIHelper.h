// Copyright XiaoXin.  Year : 2021

#pragma once

#include "CoreMinimal.h"

#if WITH_CEF3

#include "Layout/Geometry.h"

class FSlateTexture2DRHIRef;
class FSlateUpdatableTexture;

/**
 * Implementation of RHI renderer details for the CEF accelerated rendering path
 */
class FCEFWebViewBrowserWindowRHIHelper
{

public:
	/** Virtual Destructor. */
	virtual ~FCEFWebViewBrowserWindowRHIHelper();


public:
	static bool BUseRHIRenderer();
	FSlateUpdatableTexture* CreateTexture(void *ShareHandle);
	void UpdateSharedHandleTexture(void* SharedHandle, FSlateUpdatableTexture* SlateTexture, const FIntRect& DirtyIn);
	void UpdateCachedGeometry(const FGeometry& AllottedGeometry);
	TOptional<FSlateRenderTransform> GetWebViewBrowserRenderTransform() const;

private:
	FGeometry AllottedGeometry;
};

#endif
