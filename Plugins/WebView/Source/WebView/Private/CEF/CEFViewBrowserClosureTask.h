// Copyright XiaoXin.  Year : 2021

#pragma once

#include "CoreMinimal.h"

#if WITH_CEF3
#include "CEFViewLibCefIncludes.h"

// Helper for posting a closure as a task
class FCEFViewBrowserClosureTask
	: public CefTask
{
public:
	FCEFViewBrowserClosureTask(CefRefPtr<CefBaseRefCounted> InHandle, TFunction<void ()> InClosure)
		: Handle(InHandle)
		, Closure(InClosure)
	{ }

	virtual void Execute() override
	{
		Closure();
	}

private:
	CefRefPtr<CefBaseRefCounted> Handle; // Used so the handler will not go out of scope before the closure is executed.
	TFunction<void ()> Closure;
	IMPLEMENT_REFCOUNTING(FCEFViewBrowserClosureTask);
};


#endif /* WITH_CEF3 */
