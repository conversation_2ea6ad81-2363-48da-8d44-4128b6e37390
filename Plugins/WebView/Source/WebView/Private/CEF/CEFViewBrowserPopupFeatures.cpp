// Copyright XiaoXin.  Year : 2021

#include "CEF/CEFViewBrowserPopupFeatures.h"

#if WITH_CEF3

FCEFViewBrowserPopupFeatures::FCEFViewBrowserPopupFeatures()
	: X(0)
	, bXSet(false)
	, Y(0)
	, bYSet(false)
	, Width(0)
	, bWidthSet(false)
	, Height(0)
	, bHeightSet(false)
	, bMenuBarVisible(true)
	, bStatusBarVisible(false)
	, bToolBarVisible(true)
	, bLocationBarVisible(true)
	, bScrollbarsVisible(true)
	, bResizable(true)
	, bIsFullscreen(false)
	, bIsDialog(false)
{
}


FCEFViewBrowserPopupFeatures::FCEFViewBrowserPopupFeatures( const CefPopupFeatures& PopupFeatures )
{
	X = PopupFeatures.x;
	bXSet = PopupFeatures.xSet ? true : false;
	Y = PopupFeatures.y;
	bYSet = PopupFeatures.ySet ? true : false;
	Width = PopupFeatures.width;
	bWidthSet = PopupFeatures.widthSet ? true : false;
	Height = PopupFeatures.height;
	bHeightSet = PopupFeatures.heightSet ? true : false;
	bMenuBarVisible = PopupFeatures.menuBarVisible ? true : false;
	bStatusBarVisible = PopupFeatures.statusBarVisible ? true : false;
	bToolBarVisible = PopupFeatures.toolBarVisible ? true : false;
	bScrollbarsVisible = PopupFeatures.scrollbarsVisible ? true : false;
	
	// no longer set by the CEF API so default them here to their historic value
	bLocationBarVisible = false;
	bResizable = false;
	bIsFullscreen = false;
	bIsDialog = false;
}

FCEFViewBrowserPopupFeatures::~FCEFViewBrowserPopupFeatures()
{
}

void FCEFViewBrowserPopupFeatures::SetResizable(const bool bResize)
{
	bResizable = bResize;
}

int FCEFViewBrowserPopupFeatures::GetX() const
{
	return X;
}

bool FCEFViewBrowserPopupFeatures::IsXSet() const
{
	return bXSet;
}

int FCEFViewBrowserPopupFeatures::GetY() const
{
	return Y;
}

bool FCEFViewBrowserPopupFeatures::IsYSet() const
{
	return bYSet;
}

int FCEFViewBrowserPopupFeatures::GetWidth() const
{
	return Width;
}

bool FCEFViewBrowserPopupFeatures::IsWidthSet() const
{
	return bWidthSet;
}

int FCEFViewBrowserPopupFeatures::GetHeight() const
{
	return Height;
}

bool FCEFViewBrowserPopupFeatures::IsHeightSet() const
{
	return bHeightSet;
}

bool FCEFViewBrowserPopupFeatures::IsMenuBarVisible() const
{
	return bMenuBarVisible;
}

bool FCEFViewBrowserPopupFeatures::IsStatusBarVisible() const
{
	return bStatusBarVisible;
}

bool FCEFViewBrowserPopupFeatures::IsToolBarVisible() const
{
	return bToolBarVisible;
}

bool FCEFViewBrowserPopupFeatures::IsLocationBarVisible() const
{
	return bLocationBarVisible;
}

bool FCEFViewBrowserPopupFeatures::IsScrollbarsVisible() const
{
	return bScrollbarsVisible;
}

bool FCEFViewBrowserPopupFeatures::IsResizable() const
{
	return bResizable;
}

bool FCEFViewBrowserPopupFeatures::IsFullscreen() const
{
	return bIsFullscreen;
}

bool FCEFViewBrowserPopupFeatures::IsDialog() const
{
	return bIsDialog;
}

TArray<FString> FCEFViewBrowserPopupFeatures::GetAdditionalFeatures() const
{
	TArray<FString> Empty;
	return Empty;
}

#endif
