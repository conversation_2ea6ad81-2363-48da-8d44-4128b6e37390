// Copyright XiaoXin.  Year : 2021


#include "NativeWebViewBrowserProxy.h"
#include "NativeViewJSScripting.h"
#include "Misc/EmbeddedCommunication.h"


FNativeWebViewBrowserProxy::FNativeWebViewBrowserProxy(bool bInJSBindingToLoweringEnabled)
	: bJSBindingToLoweringEnabled(bInJSBindingToLoweringEnabled)
{

}

void FNativeWebViewBrowserProxy::Initialize()
{
	Scripting = MakeShareable(new FNativeViewJSScripting(bJSBindingToLoweringEnabled, SharedThis(this)));
	FEmbeddedDelegates::GetNativeToEmbeddedParamsDelegateForSubsystem(TEXT("browserProxy")).AddRaw(this, &FNativeWebViewBrowserProxy::HandleEmbeddedCommunication);
}

FNativeWebViewBrowserProxy::~FNativeWebViewBrowserProxy()
{
	FEmbeddedDelegates::GetNativeToEmbeddedParamsDelegateForSubsystem(TEXT("browserProxy")).RemoveAll(this);
}

bool FNativeWebViewBrowserProxy::OnJsMessageReceived(const FString& Message)
{
	return Scripting->OnJsMessageReceived(Message);
}

void FNativeWebViewBrowserProxy::HandleEmbeddedCommunication(const FEmbeddedCallParamsHelper& Params)
{
	FString Error;
	if (Params.Command == "handlejs")
	{
		FString Message = Params.Parameters.FindRef(TEXT("script"));
		if (!Message.IsEmpty())
		{
			if (!OnJsMessageReceived(Message))
			{
				Error = TEXT("Command failed");
			}
		}
	}
	else if (Params.Command == "pageload")
	{
		Scripting->PageLoaded();
	}

	Params.OnCompleteDelegate(FEmbeddedCommunicationMap(), Error);
}

void FNativeWebViewBrowserProxy::LoadURL(FString NewURL)
{
}

void FNativeWebViewBrowserProxy::LoadString(FString Contents, FString DummyURL)
{
}

void FNativeWebViewBrowserProxy::SetViewportSize(FIntPoint WindowSize, FIntPoint WindowPos)
{
}

FIntPoint FNativeWebViewBrowserProxy::GetViewportSize() const
{
	return FIntPoint(ForceInitToZero);
}

FSlateShaderResource* FNativeWebViewBrowserProxy::GetTexture(bool bIsPopup /*= false*/)
{
	return nullptr;
}

bool FNativeWebViewBrowserProxy::IsValid() const
{
	return false;
}

bool FNativeWebViewBrowserProxy::IsInitialized() const
{
	return true;
}

bool FNativeWebViewBrowserProxy::IsClosing() const
{
	return false;
}

EWebViewBrowserDocumentState FNativeWebViewBrowserProxy::GetDocumentLoadingState() const
{
	return EWebViewBrowserDocumentState::Loading;
}

FString FNativeWebViewBrowserProxy::GetTitle() const
{
	return TEXT("");
}

FString FNativeWebViewBrowserProxy::GetUrl() const
{
	return TEXT("");
}

void FNativeWebViewBrowserProxy::GetSource(TFunction<void(const FString&)> Callback) const
{
	Callback(FString());
}

void FNativeWebViewBrowserProxy::SetSupportsMouseWheel(bool bValue)
{

}

bool FNativeWebViewBrowserProxy::GetSupportsMouseWheel() const
{
	return false;
}

bool FNativeWebViewBrowserProxy::OnKeyDown(const FKeyEvent& InKeyEvent)
{
	return false;
}

bool FNativeWebViewBrowserProxy::OnKeyUp(const FKeyEvent& InKeyEvent)
{
	return false;
}

bool FNativeWebViewBrowserProxy::OnKeyChar(const FCharacterEvent& InCharacterEvent)
{
	return false;
}

FReply FNativeWebViewBrowserProxy::OnMouseButtonDown(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, bool bIsPopup)
{
	return FReply::Unhandled();
}

FReply FNativeWebViewBrowserProxy::OnMouseButtonUp(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, bool bIsPopup)
{
	return FReply::Unhandled();
}

FReply FNativeWebViewBrowserProxy::OnMouseButtonDoubleClick(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, bool bIsPopup)
{
	return FReply::Unhandled();
}

FReply FNativeWebViewBrowserProxy::OnMouseMove(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, bool bIsPopup)
{
	return FReply::Unhandled();
}

void FNativeWebViewBrowserProxy::OnMouseLeave(const FPointerEvent& MouseEvent)
{
}

FReply FNativeWebViewBrowserProxy::OnMouseWheel(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, bool bIsPopup)
{
	return FReply::Unhandled();
}


void FNativeWebViewBrowserProxy::OnFocus(bool SetFocus, bool bIsPopup)
{
}

void FNativeWebViewBrowserProxy::OnCaptureLost()
{
}

bool FNativeWebViewBrowserProxy::CanGoBack() const
{
	return false;
}

void FNativeWebViewBrowserProxy::GoBack()
{
}

bool FNativeWebViewBrowserProxy::CanGoForward() const
{
	return false;
}

void FNativeWebViewBrowserProxy::GoForward()
{
}

bool FNativeWebViewBrowserProxy::IsLoading() const
{
	return false;
}

void FNativeWebViewBrowserProxy::Reload()
{
}

void FNativeWebViewBrowserProxy::StopLoad()
{
}

void FNativeWebViewBrowserProxy::ExecuteJavascript(const FString& Script)
{
	FEmbeddedCallParamsHelper CallHelper;
	CallHelper.Command = TEXT("execjs");
	CallHelper.Parameters = { { TEXT("script"), Script } };

	FEmbeddedDelegates::GetEmbeddedToNativeParamsDelegateForSubsystem(TEXT("webview")).Broadcast(CallHelper);
}

void FNativeWebViewBrowserProxy::CloseBrowser(bool bForce)
{
}

void FNativeWebViewBrowserProxy::BindUObject(const FString& Name, UObject* Object, bool bIsPermanent /*= true*/)
{
	Scripting->BindUObject(Name, Object, bIsPermanent);
}

void FNativeWebViewBrowserProxy::UnbindUObject(const FString& Name, UObject* Object /*= nullptr*/, bool bIsPermanent /*= true*/)
{
	Scripting->UnbindUObject(Name, Object, bIsPermanent);
}

int FNativeWebViewBrowserProxy::GetLoadError()
{
	return 0;
}

void FNativeWebViewBrowserProxy::SetIsDisabled(bool bValue)
{
}

TSharedPtr<SWindow> FNativeWebViewBrowserProxy::GetParentWindow() const
{
	return nullptr;
}

void FNativeWebViewBrowserProxy::SetParentWindow(TSharedPtr<SWindow> Window)
{
}
