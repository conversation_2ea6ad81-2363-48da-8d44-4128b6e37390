// Copyright XiaoXin.  Year : 2021

#pragma once

#include "CoreMinimal.h"

#include "WebViewJSFunction.h"
#include "WebViewJSScripting.h"

typedef TSharedRef<class FNativeViewJSScripting> FNativeViewJSScriptingRef;
typedef TSharedPtr<class FNativeViewJSScripting> FNativeViewJSScriptingPtr;

class FNativeWebViewBrowserProxy;

/**
 * Implements handling of bridging UObjects client side with JavaScript renderer side.
 */
class FNativeViewJSScripting
	: public FWebViewJSScripting
	, public TSharedFromThis<FNativeViewJSScripting>
{
public:
	//static const FString JSMessageTag;

	FNativeViewJSScripting(bool bJSBindingToLoweringEnabled, TSharedRef<FNativeWebViewBrowserProxy> Window);

	virtual void BindUObject(const FString& Name, UObject* Object, bool bIsPermanent = true) override;
	virtual void UnbindUObject(const FString& Name, UObject* Object = nullptr, bool bIsPermanent = true) override;

	bool OnJsMessageReceived(const FString& Message);

	FString ConvertStruct(UStruct* TypeInfo, const void* StructPtr);
	FString ConvertObject(UObject* Object);

	virtual void InvokeJSFunction(FGuid FunctionId, int32 ArgCount, FWebViewJSParam Arguments[], bool bIsError=false) override;
	virtual void InvokeJSErrorResult(FGuid FunctionId, const FString& Error) override;
	void PageLoaded();

private:
	FString GetInitializeScript();
	void InvokeJSFunctionRaw(FGuid FunctionId, const FString& JSValue, bool bIsError=false);
	bool IsValid()
	{
		return WindowPtr.Pin().IsValid();
	}

	/** Message handling helpers */
	bool HandleExecuteUObjectMethodMessage(const TArray<FString>& Params);
	void ExecuteJavascript(const FString& Javascript);

	TWeakPtr<FNativeWebViewBrowserProxy> WindowPtr;
	bool bLoaded;
};
