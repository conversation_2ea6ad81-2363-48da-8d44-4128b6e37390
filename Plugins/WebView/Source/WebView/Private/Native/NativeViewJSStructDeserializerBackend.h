// Copyright XiaoXin.  Year : 2021

#pragma once

#include "CoreMinimal.h"
#include "NativeViewJSScripting.h"
#include "Backends/JsonStructDeserializerBackend.h"
#include "Serialization/MemoryReader.h"

class FNativeViewJSStructDeserializerBackend
	: public FJsonStructDeserializerBackend
{
public:
	FNativeViewJSStructDeserializerBackend(FNativeViewJSScriptingRef InScripting, FMemoryReader& Reader);

	virtual bool ReadProperty( FProperty* Property, FProperty* Outer, void* Data, int32 ArrayIndex ) override;

private:
	FNativeViewJSScriptingRef Scripting;

};
