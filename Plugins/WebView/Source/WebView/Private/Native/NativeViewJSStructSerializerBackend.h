// Copyright XiaoXin.  Year : 2021

#pragma once

#include "CoreMinimal.h"
#include "NativeViewJSScripting.h"
#include "Backends/JsonStructSerializerBackend.h"

class UObject;

/**
 * Implements a writer for UStruct serialization using JavaScript.
 *
 * Based on FJsonStructSerializerBackend, it adds support for certain object types not representable in pure JSON
 *
 */
class FNativeViewJSStructSerializerBackend
	: public FJsonStructSerializerBackend
{
public:

	/**
	 * Creates and initializes a new instance.
	 *
	 * @param InScripting An instance of a web browser scripting obnject.
	 */
	FNativeViewJSStructSerializerBackend(FNativeViewJSScriptingRef InScripting, FMemoryWriter& Writer);

public:
	virtual void WriteProperty(const FStructSerializerState& State, int32 ArrayIndex = 0) override;

private:
	void WriteUObject(const FStructSerializerState& State, UObject* Value);

	FNativeViewJSScriptingRef Scripting;
};
