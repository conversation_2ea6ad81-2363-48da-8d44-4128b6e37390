// Copyright Epic Games, Inc. All Rights Reserved.

#include "BPFunctionLibBPLibrary.h"
#include "BPFunctionLib.h"
#include "ImageUtils.h"
#include "Engine/Texture2D.h"
#include "Misc/SecureHash.h"
#include "SocketSubsystem.h"
#include "Components/PrimitiveComponent.h"
#include <windows.h>
#include <iostream>
#include <string.h>
#include "Iphlpapi.h"
#include<intrin.h>
#include <winioctl.h>
#include "XmlParser.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"

#if PLATFORM_WINDOWS
#include "Windows/AllowWindowsPlatformTypes.h"
#include "Windows/HideWindowsPlatformTypes.h"
#endif

UBPFunctionLibBPLibrary::UBPFunctionLibBPLibrary(const FObjectInitializer& ObjectInitializer)
: Super(ObjectInitializer)
{
	
}

float UBPFunctionLibBPLibrary::BPFunctionLibSampleFunction(float Param)
{
	return -1;
}

FButtonStyle UBPFunctionLibBPLibrary::LoadImageAndCreateButtonStyle(FString LoadPath)
{

	UTexture2D* LoadedTexture = FImageUtils::ImportFileAsTexture2D(LoadPath);
	FButtonStyle BtnStyle = FButtonStyle();
	if (!LoadedTexture)
	{
		UE_LOG(LogTemp, Log, TEXT("FilePath:%s is not exit"), *LoadPath); 
		return BtnStyle;
	}
	
	FVector2D ImageSize(LoadedTexture->GetSizeX(), LoadedTexture->GetSizeY());
	FSlateBrush Brush;
	Brush.SetResourceObject(LoadedTexture);
	Brush.SetImageSize(ImageSize);

	
	BtnStyle.SetNormal(Brush);
	BtnStyle.SetHovered(Brush);
	BtnStyle.SetPressed(Brush);

	

	return BtnStyle;
}

FString UBPFunctionLibBPLibrary::MD5Encryption(FString SourceStr)
{
	return FMD5::HashAnsiString(*SourceStr);
}

void UBPFunctionLibBPLibrary::OpenExe(FString FilePath)
{
	std::string str_path = TCHAR_TO_UTF8(*FilePath);
	std::wstring wstr_path;
	wstr_path.assign(str_path.begin(), str_path.end());
	ShellExecute(NULL, L"open", wstr_path.c_str(), NULL, NULL, SW_HIDE);
}

void UBPFunctionLibBPLibrary::CloseExe(FString ProcessName)
{
	std::string process = std::string("TASKKILL /F /IM ") + TCHAR_TO_UTF8(*ProcessName);
	system(process.c_str());
}

void UBPFunctionLibBPLibrary::SetComponentAffectDistanceFieldLighting(UPrimitiveComponent* theComponent, bool bIsAffectDistanceFieldLighting)
{
	theComponent->bAffectDistanceFieldLighting = bIsAffectDistanceFieldLighting;
}

FString UBPFunctionLibBPLibrary::GetIntranetIp()
{
	FString strLocalIp;
	bool bCanBind = false;
	TSharedRef<FInternetAddr> LocalIp = ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->GetLocalHostAddr(*GLog, bCanBind);
	if (LocalIp->IsValid())
	{
		strLocalIp = LocalIp->ToString(false);
	}

	return strLocalIp;
}

FString UBPFunctionLibBPLibrary::GetLocalHostName()
{
	FString HostName;
	ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->GetHostName(HostName);
	return HostName;
}

FString UBPFunctionLibBPLibrary::GetHardwareID()
{
	return FPlatformMisc::GetLoginId();
}

FString UBPFunctionLibBPLibrary::GetOSName()
{
	FString OSLabel, OSVersion;
	FPlatformMisc::GetOSVersions(OSLabel, OSVersion);
	return FString::Printf(TEXT("%s %s"), *OSLabel, *OSVersion);
}

FString UBPFunctionLibBPLibrary::GetCPUName()
{
	return FPlatformMisc::GetCPUBrand();
}

FString UBPFunctionLibBPLibrary::GetGPUName()
{
	return FPlatformMisc::GetPrimaryGPUBrand();
}

FString UBPFunctionLibBPLibrary::GetOSID()
{
	return FPlatformMisc::GetOperatingSystemId();
}

bool UBPFunctionLibBPLibrary::Is64BitOS()
{
	return FPlatformMisc::Is64bitOperatingSystem();
}

void UBPFunctionLibBPLibrary::GetScreenSize(int32& X, int32& Y)
{
	X = GetSystemMetrics(SM_CXSCREEN);
	Y = GetSystemMetrics(SM_CYSCREEN);
}

FString UBPFunctionLibBPLibrary::GetMachineID()
{

	return FPlatformMisc::GetMachineId().ToString();
}

FString UBPFunctionLibBPLibrary::GetCpuInfoYunTo()
{
	



	uint32 CPUInfo = FPlatformMisc::GetCPUInfo();
	UE_LOG(LogTemp, Warning, TEXT("CPU Info: %u"), CPUInfo);


	// �ֶ�������Щλ
	int Stepping = CPUInfo & 0xF;
	int Model = (CPUInfo >> 4) & 0xF;
	int Family = (CPUInfo >> 8) & 0xF;
	int ProcessorType = (CPUInfo >> 12) & 0x3;
	int ExtendedModel = (CPUInfo >> 16) & 0xF;
	int ExtendedFamily = (CPUInfo >> 20) & 0xFF;

	UE_LOG(LogTemp, Warning, TEXT("Stepping: %d, Model: %d, Family: %d, Processor Type: %d, Extended Model: %d, Extended Family: %d"),
		Stepping, Model, Family, ProcessorType, ExtendedModel, ExtendedFamily);

	return  FString::FromInt(CPUInfo);

}

void UBPFunctionLibBPLibrary::AdjustString(const char* str, int pos, char* buf)
{

	int i = 0, nStart, nEnd;

	nStart = pos;
	while ((str[nStart] == ' '))
		nStart++;

	nEnd = nStart;
	while (str[nEnd])
	{
		buf[i++] = str[nEnd];
		nEnd++;
	}
	buf[i] = '\0';


}

bool UBPFunctionLibBPLibrary::GetMacAddress(int nNetIndex, char* sAddress)
{
	int nIndex = 0;
	char sTmp[10];
	//PIP_ADAPTER_INFO�ṹ��ָ��洢����������Ϣ
	PIP_ADAPTER_INFO pIpAdapterInfo = new IP_ADAPTER_INFO();
	//�õ��ṹ���С,����GetAdaptersInfo����
	unsigned long stSize = sizeof(IP_ADAPTER_INFO);
	//����GetAdaptersInfo����,���pIpAdapterInfoָ�����;����stSize��������һ��������Ҳ��һ�������
	int nRel = GetAdaptersInfo(pIpAdapterInfo, &stSize);
	if (ERROR_BUFFER_OVERFLOW == nRel)
	{
		//����������ص���ERROR_BUFFER_OVERFLOW
		//��˵��GetAdaptersInfo�������ݵ��ڴ�ռ䲻��,ͬʱ�䴫��stSize,��ʾ��Ҫ�Ŀռ��С
		//��Ҳ��˵��ΪʲôstSize����һ��������Ҳ��һ�������
		//�ͷ�ԭ�����ڴ�ռ�
		delete pIpAdapterInfo;
		//���������ڴ�ռ������洢����������Ϣ
		pIpAdapterInfo = (PIP_ADAPTER_INFO)new BYTE[stSize];
		//�ٴε���GetAdaptersInfo����,���pIpAdapterInfoָ�����
		nRel = GetAdaptersInfo(pIpAdapterInfo, &stSize);
	}
	if (ERROR_SUCCESS == nRel)
	{
		//�������������Ϣ
		/*//���������Ϣ
		while (pIpAdapterInfo)
		{
			if (nIndex == nNetIndex)
			{
				for (UINT i = 0; i < pIpAdapterInfo->AddressLength; i++)
				{
					sprintf(sTmp, "%02X", pIpAdapterInfo->Address[i]);
					strcat(sAddress, sTmp);
				}
				return true;
			}

			nIndex++;
			pIpAdapterInfo = pIpAdapterInfo->Next;
		}*/

		//�������������Ϣ
		while (pIpAdapterInfo)
		{

			// ����Ƿ�Ϊ��������
			bool isPhysicalAdapter = false;

			if (pIpAdapterInfo->Type == MIB_IF_TYPE_ETHERNET)
			{
				// ����������Ƿ����������������
				const char* virtualKeywords[] = {
					"VMware", "VirtualBox", "Hyper-V", "Virtual",
					"TAP", "VPN", "Tunnel", "Pseudo"
				};

				bool isVirtual = false;
				for (const char* keyword : virtualKeywords)
				{
					if (strstr(pIpAdapterInfo->Description, keyword) != NULL)
					{
						isVirtual = true;
						break;
					}
				}

				// ���MAC��ַǰ׺�Ƿ�Ϊ����������������
				char macPrefix[7] = { 0 };
				sprintf(macPrefix, "%02X%02X%02X",
					pIpAdapterInfo->Address[0],
					pIpAdapterInfo->Address[1],
					pIpAdapterInfo->Address[2]);

				const char* virtualMacPrefixes[] = {
					"000569", // VMware
					"000C29", // VMware
					"001C14", // VMware
					"005056", // VMware
					"080027", // VirtualBox
					"001C42", // Parallels
					"0A0027"  // Microsoft Hyper-V
				};

				bool isVirtualMac = false;
				for (const char* prefix : virtualMacPrefixes)
				{
					if (strcmp(macPrefix, prefix) == 0)
					{
						isVirtualMac = true;
						break;
					}
				}

				// ����Ȳ�����������Ҳ��������MAC������Ϊ����������
				if (!isVirtual && !isVirtualMac)
				{
					isPhysicalAdapter = true;
				}
			}

			if (isPhysicalAdapter)
			{
				if (nIndex == nNetIndex)
				{
					for (UINT i = 0; i < pIpAdapterInfo->AddressLength; i++)
					{
						sprintf(sTmp, "%02X", pIpAdapterInfo->Address[i]);
						strcat(sAddress, sTmp);
					}
					return true;
				}
				nIndex++;
			}

			pIpAdapterInfo = pIpAdapterInfo->Next;

		}

	}
	//�ͷ��ڴ�ռ�
	if (pIpAdapterInfo)
	{
		delete pIpAdapterInfo;
	}

	return false;

}

ULONG UBPFunctionLibBPLibrary::GetHDSerial(char* pszIDBuff, int nBuffLen, int nDriveID)
{
	//char pszIDBuff[256];
	HANDLE hPhysicalDrive = INVALID_HANDLE_VALUE;
	ULONG ulSerialLen = 0;

	//  Try to get a handle to PhysicalDrive IOCTL, report failure
	//  and exit if can't.
	TCHAR szDriveName[32];
	wsprintf(szDriveName, TEXT("\\\\.\\PhysicalDrive%d"), nDriveID);
	//  Windows NT, Windows 2000, Windows XP - admin rights not required
	hPhysicalDrive = CreateFile(szDriveName, 0,
		FILE_SHARE_READ | FILE_SHARE_WRITE, NULL,
		OPEN_EXISTING, 0, NULL);
	if (hPhysicalDrive == INVALID_HANDLE_VALUE)
	{
		return false;
	}
	STORAGE_PROPERTY_QUERY query;
	DWORD cbBytesReturned = 0;
	static char local_buffer[10000];
	memset((void*)&query, 0, sizeof(query));
	query.PropertyId = StorageDeviceProperty;
	query.QueryType = PropertyStandardQuery;
	memset(local_buffer, 0, sizeof(local_buffer));
	if (DeviceIoControl(hPhysicalDrive, IOCTL_STORAGE_QUERY_PROPERTY,
		&query,
		sizeof(query),
		&local_buffer[0],
		sizeof(local_buffer),
		&cbBytesReturned, NULL))
	{
		STORAGE_DEVICE_DESCRIPTOR* descrip = (STORAGE_DEVICE_DESCRIPTOR*)&local_buffer;
		char serialNumber[1000];
		AdjustString(local_buffer, descrip->SerialNumberOffset, serialNumber);
		if (isalnum(serialNumber[0]))
		{
			ULONG ulSerialLenTemp = (ULONG)strnlen(serialNumber, nBuffLen - 1);
			memcpy(pszIDBuff, serialNumber, ulSerialLenTemp);
			pszIDBuff[ulSerialLenTemp] = NULL;
			ulSerialLen = ulSerialLenTemp;
		}
	}
	if (hPhysicalDrive != INVALID_HANDLE_VALUE)
	{
		CloseHandle(hPhysicalDrive);
	}
	return ulSerialLen;
	
}

TArray<FString> UBPFunctionLibBPLibrary::GetHDSerial()
{
	TArray<FString> HDSerial;
	HDSerial.Empty();
	char szBuff[256];
	const int MAX_IDE_DRIVES = 16;
	for (int nDriveNum = 0; nDriveNum < MAX_IDE_DRIVES; nDriveNum++)
	{
		ULONG ulLen = GetHDSerial(szBuff, sizeof(szBuff), nDriveNum);
		if (ulLen > 0)
		{
			//_tprintf(TEXT("��%d��Ӳ�̵����к�Ϊ��%hs\n"), nDriveNum + 1, szBuff);
			//printf("Disk Serial No.%d: %s\n", nDriveNum + 1, szBuff);
			HDSerial.Add(szBuff);
		}
	}
	UE_LOG(LogTemp, Warning, TEXT("BPFunction***  jiami ***  YunnToHDSerial---%s  ***"), *HDSerial[0]);
	return HDSerial;

}

FString UBPFunctionLibBPLibrary::GetUniqueMacAddress()
{
	char sBuf[50] = "";
	// ��ȡ��һ������������MAC��ַ (nNetNum = 0)
	if (GetMacAddress(0, sBuf))
	{
		return FString(sBuf);
	}

	// ���û���ҵ��κ����������ؿ��ַ���
	UE_LOG(LogTemp, Warning, TEXT("BPFunction*** YunnToMacAddress---%s ***"), *FString(sBuf));
	return FString();
}

FString UBPFunctionLibBPLibrary::GetCPUAddresss()
{
	FString cpuAddress;
	INT32 deBuf[4];
	__cpuid(deBuf, 01);
	//printf("CPUID:%.8X%.8X\n", deBuf[0], deBuf[3]);
	//cpuAddress = FString::FromInt(deBuf[0]);
	cpuAddress = FString::Printf(TEXT("%.8X%.8X"), deBuf[0], deBuf[3]);
	//cpuAddress.Append(FString::FromInt(deBuf[3]));
	UE_LOG(LogTemp, Warning, TEXT("BPFunction***  jiami ***   YunnToCPUAddresss---%s  ***"), *cpuAddress);
	return cpuAddress;
}

FString UBPFunctionLibBPLibrary::GetMotherboardSerial()
{

#if PLATFORM_WINDOWS
	BOOL bRet = false;
	HANDLE hReadPipe = NULL;
	HANDLE hWritePipe = NULL;
	PROCESS_INFORMATION pi;
	STARTUPINFOW si;
	SECURITY_ATTRIBUTES sa;

	WCHAR szFetCmd[] = L"wmic BaseBoard get SerialNumber";
	const FString strEnSearch = TEXT("SerialNumber");

	// ��ʼ��������
	CHAR szBuffer[MAX_COMMAND_SIZE + 1] = { 0 };

	// ��ʼ���ṹ��
	FMemory::Memzero(&pi, sizeof(pi));
	FMemory::Memzero(&si, sizeof(si));
	FMemory::Memzero(&sa, sizeof(sa));

	pi.hProcess = NULL;
	pi.hThread = NULL;
	si.cb = sizeof(STARTUPINFO);
	sa.nLength = sizeof(SECURITY_ATTRIBUTES);
	sa.lpSecurityDescriptor = NULL;
	sa.bInheritHandle = true;

	// �����ܵ�
	bRet = CreatePipe(&hReadPipe, &hWritePipe, &sa, 0);
	if (!bRet)
	{
		return TEXT("Warning Failed to create pipe");
	}

	// ����������Ϣ
	GetStartupInfoW(&si);
	si.hStdError = hWritePipe;
	si.hStdOutput = hWritePipe;
	si.wShowWindow = SW_HIDE;
	si.dwFlags = STARTF_USESHOWWINDOW | STARTF_USESTDHANDLES;

	// ��������
	bRet = CreateProcessW(NULL, szFetCmd, NULL, NULL, true, 0, NULL, NULL, &si, &pi);
	if (!bRet)
	{
		CloseHandle(hWritePipe);
		CloseHandle(hReadPipe);
		return TEXT("Warning Failed to create process");
	}

	// ��ȡ����
	DWORD count = 0;
	WaitForSingleObject(pi.hProcess, 500);
	bRet = ReadFile(hReadPipe, szBuffer, MAX_COMMAND_SIZE, &count, 0);
	if (!bRet)
	{
		CloseHandle(hWritePipe);
		CloseHandle(hReadPipe);
		CloseHandle(pi.hProcess);
		CloseHandle(pi.hThread);
		return TEXT("Warning Failed to read data");
	}

	// ����������
	FString strBuffer = UTF8_TO_TCHAR(szBuffer);
	int32 iPos = strBuffer.Find(strEnSearch);

	if (iPos < 0)
	{
		CloseHandle(hWritePipe);
		CloseHandle(hReadPipe);
		CloseHandle(pi.hProcess);
		CloseHandle(pi.hThread);
		return TEXT("Warning Serial number not found");
	}

	// ��ȡ���к�
	FString result = strBuffer.RightChop(iPos + strEnSearch.Len());

	// ��������ַ���
	result.ReplaceInline(TEXT(" "), TEXT(""));
	result.ReplaceInline(TEXT("\r"), TEXT(""));
	result.ReplaceInline(TEXT("\n"), TEXT(""));
	result = result.TrimStartAndEnd();

	// �������
	CloseHandle(hWritePipe);
	CloseHandle(hReadPipe);
	CloseHandle(pi.hProcess);
	CloseHandle(pi.hThread);
	UE_LOG(LogTemp, Warning, TEXT("BPFunction***   jiami ***   YunnToMotherboardSerial---%s  ***"), *result);

	return result;
#else
	return TEXT("Warning Platform not supported");
#endif

}

FString UBPFunctionLibBPLibrary::GetYunnToUniqueID()
{
	//cpu + mac + motherboard + HDSerial �� MD5����
	FString YunnToUniqueID = FString::Printf(TEXT("%s%s%s%s"), *GetCPUAddresss(), *GetMotherboardSerial(), *GetUniqueMacAddress(), *GetHDSerial()[0]);
	UE_LOG(LogTemp, Warning, TEXT("BPFunction*** YunnToUniqueID---%s  ***"), *YunnToUniqueID);

	return MD5Encryption(YunnToUniqueID);
}

TMap<FString, FString> UBPFunctionLibBPLibrary::ReadExcelToKeyValuePairs(FString FilePath)
{
	TMap<FString, FString> ResultMap;

	// 检查文件是否存在
	if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("File not found: %s"), *FilePath);
		return ResultMap;
	}

	FString FileContent;

	// 支持CSV和XLSX文件
	if (FilePath.EndsWith(TEXT(".csv")))
	{
		// 读取CSV文件
		if (!FFileHelper::LoadFileToString(FileContent, *FilePath))
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to read CSV file: %s"), *FilePath);
			return ResultMap;
		}

		// 解析CSV内容
		TArray<FString> Lines;
		FileContent.ParseIntoArrayLines(Lines);

		// 跳过标题行，从第二行开始处理数据
		for (int32 i = 1; i < Lines.Num(); i++)
		{
			FString Line = Lines[i].TrimStartAndEnd();
			if (Line.IsEmpty()) continue;

			TArray<FString> Columns;
			Line.ParseIntoArray(Columns, TEXT(","), true);

			// 确保至少有3列数据
			if (Columns.Num() >= 3)
			{
				FString Name = Columns[0].TrimStartAndEnd().TrimQuotes();
				FString Alias = Columns[1].TrimStartAndEnd().TrimQuotes();
				FString EncryptionName = Columns[2].TrimStartAndEnd().TrimQuotes();

				// 构建Key: name*alias 或者 name（如果alias为空）
				FString Key;
				if (!Alias.IsEmpty())
				{
					Key = FString::Printf(TEXT("%s*%s"), *Name, *Alias);
				}
				else
				{
					Key = Name;
				}

				// 添加到结果Map中
				if (!Key.IsEmpty() && !EncryptionName.IsEmpty())
				{
					ResultMap.Add(Key, EncryptionName);
					UE_LOG(LogTemp, Log, TEXT("Added Key-Value pair: %s -> %s"), *Key, *EncryptionName);
				}
			}
		}

		UE_LOG(LogTemp, Warning, TEXT("Successfully processed CSV file. Found %d key-value pairs."), ResultMap.Num());
	}
	else if (FilePath.EndsWith(TEXT(".xlsx")))
	{
		// 对于XLSX文件，提供转换建议
		UE_LOG(LogTemp, Warning, TEXT("XLSX files are not directly supported. Please convert your Excel file to CSV format."));
		UE_LOG(LogTemp, Warning, TEXT("Steps to convert:"));
		UE_LOG(LogTemp, Warning, TEXT("1. Open your Excel file"));
		UE_LOG(LogTemp, Warning, TEXT("2. Go to File -> Save As"));
		UE_LOG(LogTemp, Warning, TEXT("3. Choose 'CSV (Comma delimited)' format"));
		UE_LOG(LogTemp, Warning, TEXT("4. Save the file and use the CSV version with this function"));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Unsupported file format. Please use .csv or .xlsx files."));
	}

	return ResultMap;
}

TMap<FString, FString> UBPFunctionLibBPLibrary::ReadExcelFileWithCOM(FString FilePath)
{
	TMap<FString, FString> ResultMap;

#if PLATFORM_WINDOWS
	// 检查文件是否存在
	if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("Excel file not found: %s"), *FilePath);
		return ResultMap;
	}

	// 由于COM Excel自动化比较复杂且需要安装Excel，这里提供一个简化的实现
	// 建议使用PowerShell脚本来转换Excel到CSV，然后读取CSV

	FString PowerShellScript = FString::Printf(TEXT(
		"$excel = New-Object -ComObject Excel.Application; "
		"$excel.Visible = $false; "
		"$excel.DisplayAlerts = $false; "
		"$workbook = $excel.Workbooks.Open('%s'); "
		"$worksheet = $workbook.Worksheets.Item(1); "
		"$csvPath = '%s.csv'; "
		"$worksheet.SaveAs($csvPath, 6); "
		"$workbook.Close($false); "
		"$excel.Quit(); "
		"[System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel)"
	), *FilePath, *FilePath);

	// 执行PowerShell脚本转换Excel到CSV
	FString Command = FString::Printf(TEXT("powershell.exe -Command \"%s\""), *PowerShellScript);

	STARTUPINFO si;
	PROCESS_INFORMATION pi;
	ZeroMemory(&si, sizeof(si));
	si.cb = sizeof(si);
	si.dwFlags = STARTF_USESHOWWINDOW;
	si.wShowWindow = SW_HIDE;
	ZeroMemory(&pi, sizeof(pi));

	// 转换FString到TCHAR数组
	TArray<TCHAR> CommandArray = Command.GetCharArray();

	if (CreateProcess(NULL, CommandArray.GetData(), NULL, NULL, false, 0, NULL, NULL, &si, &pi))
	{
		// 等待进程完成
		WaitForSingleObject(pi.hProcess, 10000); // 等待最多10秒

		// 关闭进程句柄
		CloseHandle(pi.hProcess);
		CloseHandle(pi.hThread);

		// 检查CSV文件是否生成成功
		FString CsvPath = FilePath + TEXT(".csv");
		if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*CsvPath))
		{
			// 使用现有的CSV读取功能
			ResultMap = ReadExcelToKeyValuePairs(CsvPath);

			// 删除临时CSV文件
			FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*CsvPath);

			UE_LOG(LogTemp, Warning, TEXT("Successfully converted Excel to CSV and processed. Found %d key-value pairs."), ResultMap.Num());
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to convert Excel file to CSV. Make sure Microsoft Excel is installed and the file is not corrupted."));
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to execute PowerShell command for Excel conversion"));
	}

#else
	UE_LOG(LogTemp, Error, TEXT("Excel reading with COM is only supported on Windows platform"));
#endif

	return ResultMap;
}

