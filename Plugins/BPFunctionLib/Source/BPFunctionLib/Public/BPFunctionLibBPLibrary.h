// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Kismet/BlueprintFunctionLibrary.h"
#include "Styling/SlateTypes.h"
#include "BPFunctionLibBPLibrary.generated.h"


/* 
*	Function library class.
*	Each function in it is expected to be static and represents blueprint node that can be called in any blueprint.
*
*	When declaring function you can define metadata for the node. Key function specifiers will be BlueprintPure and BlueprintCallable.
*	BlueprintPure - means the function does not affect the owning object in any way and thus creates a node without Exec pins.
*	BlueprintCallable - makes a function which can be executed in Blueprints - Thus it has Exec pins.
*	DisplayName - full name of the node, shown when you mouse over the node and in the blueprint drop down menu.
*				Its lets you name the node using characters not allowed in C++ function names.
*	CompactNodeTitle - the word(s) that appear on the node.
*	Keywords -	the list of keywords that helps you to find node when you search for it using Blueprint drop-down menu. 
*				Good example is "Print String" node which you can find also by using keyword "log".
*	Category -	the category your node will be under in the Blueprint drop-down menu.
*
*	For more info on custom blueprint nodes visit documentation:
*	https://wiki.unrealengine.com/Custom_Blueprint_Node_Creation
*/

UCLASS()
class UBPFunctionLibBPLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_UCLASS_BODY()

	UFUNCTION(BlueprintCallable, meta = (DisplayName = "Execute Sample function", Keywords = "BPFunctionLib sample test testing"), Category = "BPFunctionLib")
	static float BPFunctionLibSampleFunction(float Param);

	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static FButtonStyle LoadImageAndCreateButtonStyle(FString LoadPath);
	
	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static FString MD5Encryption(FString SourceStr);
	
	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
		static void OpenExe(FString FilePath);
	
	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
		static void CloseExe(FString ProcessName);

	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
		static void SetComponentAffectDistanceFieldLighting(UPrimitiveComponent* theComponent, bool bIsAffectDistanceFieldLighting);

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetIntranetIp();
	
	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetLocalHostName();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetHardwareID();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetOSName();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetCPUName();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetGPUName();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetOSID();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static bool Is64BitOS();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static void GetScreenSize(int32& X, int32& Y);
	
		UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetMachineID();

		UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetCpuInfoYunTo();



	   //��ȡ����Ӳ����Ϣ

		static void AdjustString(const char* str, int pos, char* buf);

		static bool GetMacAddress(int nNetIndex, char* sAddress);

		static ULONG GetHDSerial(char* pszIDBuff, int nBuffLen, int nDriveID);


		//��ȡ������ַ
		UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetUniqueMacAddress();


		//��ȡӲ�����к�
		UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static TArray<FString>  GetHDSerial();


		//��ȡCPU���к�
		UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetCPUAddresss();


		//��ȡ�������к�
		UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetMotherboardSerial();


		UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetYunnToUniqueID();

		// 读取Excel文件并返回键值对数组
		UFUNCTION(BlueprintCallable, Category = "BPFunctionLib", meta = (DisplayName = "Read Excel To Key Value Pairs", Keywords = "excel xlsx read keyvalue"))
		static TMap<FString, FString> ReadExcelToKeyValuePairs(FString FilePath);

		// 使用COM组件读取Excel文件（仅支持Windows）
		UFUNCTION(BlueprintCallable, Category = "BPFunctionLib", meta = (DisplayName = "Read Excel File With COM", Keywords = "excel xlsx com windows"))
		static TMap<FString, FString> ReadExcelFileWithCOM(FString FilePath);




private:
	static const int32 MAX_COMMAND_SIZE = 10000;

};
